import Redis from 'ioredis'
import { config } from './index'
import { logger } from '../utils/logger'

export const redis = new Redis({
  host: config.redis.host as string,
  port: config.redis.port as number,
  password: config.redis.password as string,
  db: config.redis.db as number,
  keyPrefix: 'enterprise_app:',
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  reconnectOnError: (err) => {
    const targetError = 'READONLY'
    return err.message.includes(targetError)
  }
})

redis.on('connect', () => {
  logger.info('Redis connected successfully')
})

redis.on('error', (err) => {
  logger.error('Redis connection error:', err)
})

redis.on('ready', () => {
  logger.info('Redis is ready')
})

redis.on('close', () => {
  logger.warn('Redis connection closed')
})

export default redis
