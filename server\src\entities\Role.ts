import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, ManyToMany, JoinTable } from 'typeorm'
import { User } from './User'
import { Menu } from './Menu'

@Entity('sys_roles')
export class Role {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ type: 'varchar', length: 50 })
  name: string

  @Column({ type: 'varchar', length: 50, unique: true })
  code: string

  @Column({ type: 'text', nullable: true })
  description?: string

  @Column({ name: 'data_scope', type: 'tinyint', default: 1 })
  dataScope: number

  @Column({ type: 'tinyint', default: 1 })
  status: number

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  // 关联关系
  @ManyToMany(() => User, user => user.roles)
  users: User[]

  @ManyToMany(() => Menu, menu => menu.roles)
  @JoinTable({
    name: 'sys_role_menus',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'menu_id', referencedColumnName: 'id' }
  })
  menus: Menu[]

  // 虚拟字段
  permissions?: string[]
  menuIds?: number[]
}
