/**
 * Creates a database exclusion.
 * Can be used on entity.
 * Can create exclusions with composite columns when used on entity.
 */
export declare function Exclusion(expression: string): ClassDecorator & PropertyDecorator;
/**
 * Creates a database exclusion.
 * Can be used on entity.
 * Can create exclusions with composite columns when used on entity.
 */
export declare function Exclusion(name: string, expression: string): ClassDecorator & PropertyDecorator;
