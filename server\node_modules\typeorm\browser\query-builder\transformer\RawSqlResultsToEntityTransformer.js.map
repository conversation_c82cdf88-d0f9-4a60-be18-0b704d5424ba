{"version": 3, "sources": ["../browser/src/query-builder/transformer/RawSqlResultsToEntityTransformer.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAI9C,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAA;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAA;AAEpD;;;GAGG;AACH,MAAM,OAAO,gCAAgC;IAezC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,aAAiC,EACjC,MAAc,EACd,oBAA4C,EAC5C,uBAAkD,EAClD,WAAyB;QAJzB,kBAAa,GAAb,aAAa,CAAoB;QACjC,WAAM,GAAN,MAAM,CAAQ;QACd,yBAAoB,GAApB,oBAAoB,CAAwB;QAC5C,4BAAuB,GAAvB,uBAAuB,CAA2B;QAClD,gBAAW,GAAX,WAAW,CAAc;QAEnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;QAC9D,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,CACrB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CACrD,CAAA;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAA;QAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAA;IACjC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,SAAS,CAAC,UAAiB,EAAE,KAAY;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;QAC3C,MAAM,QAAQ,GAAU,EAAE,CAAA;QAC1B,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YAC5D,IAAI,MAAM,KAAK,SAAS;gBAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACnD,CAAC;QACD,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,UAAU,CAAC,SAAiB,EAAE,UAAkB;QACtD,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,GAAG,IAAI,GAAG,EAAE,CAAA;YACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QAC3C,CAAC;QACD,IAAI,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QACzC,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,WAAW,GAAG,WAAW,CAAC,UAAU,CAChC,IAAI,CAAC,MAAM,EACX,SAAS,EACT,SAAS,EACT,UAAU,CACb,CAAA;YACD,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QACxC,CAAC;QACD,OAAO,WAAW,CAAA;IACtB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAiB,EAAE,KAAY;QAC3C,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAA;QACrB,MAAM,IAAI,GAAa,EAAE,CAAA;QACzB,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;YACtC,IAAI,CAAC,IAAI,CACL,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACrC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,CACnD,CACJ,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CACL,GAAG,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAC5C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,CACnD,CACJ,CAAA;QACL,CAAC;QACD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACjC,MAAM,EAAE,GAAG,IAAI;iBACV,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;gBACT,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,CAAA;gBAE/B,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC5B,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;gBACnC,CAAC;gBAED,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACjC,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBACnC,CAAC;gBAED,OAAO,QAAQ,CAAA;YACnB,CAAC,CAAC;iBACD,IAAI,CAAC,GAAG,CAAC,CAAA,CAAC,sBAAsB;YAErC,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACzB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;YAC5B,CAAC;iBAAM,CAAC;gBACJ,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACzB,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,UAAiB,EACjB,KAAY;QAEZ,uHAAuH;QACvH,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAA;QAE7B,IAAI,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YAC/B,MAAM,mBAAmB,GAAG,UAAU,CAAC,GAAG,CACtC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CACF,IAAI,CAAC,UAAU,CACX,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,QAAQ,CAAC,mBAAoB,CAAC,YAAY,CACnD,CACJ,CACR,CAAA;YACD,MAAM,qBAAqB,GAAG,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAC5D,CAAC,mBAAmB,EAAE,EAAE;gBACpB,OAAO,CACH,OAAO,mBAAmB,CAAC,IAAI,CAC3B,CAAC,KAAK,EAAE,EAAE,CACN,KAAK;oBACL,mBAAmB,CAAC,kBAAkB,CAC7C,KAAK,WAAW,CACpB,CAAA;YACL,CAAC,CACJ,CAAA;YACD,IAAI,qBAAqB;gBAAE,QAAQ,GAAG,qBAAqB,CAAA;QAC/D,CAAC;QACD,MAAM,MAAM,GAAQ,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;YAClD,gBAAgB,EAAE,IAAI;YACtB,IAAI,EAAE,IAAI,CAAC,IAAI;SAClB,CAAC,CAAA;QAEF,2EAA2E;QAC3E,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CACpC,UAAU,EACV,KAAK,EACL,MAAM,EACN,QAAQ,CACX,CAAA;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CACpC,UAAU,EACV,MAAM,EACN,KAAK,EACL,QAAQ,CACX,CAAA;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAC5C,UAAU,EACV,KAAK,EACL,MAAM,EACN,QAAQ,CACX,CAAA;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAClD,UAAU,EACV,KAAK,EACL,MAAM,CACT,CAAA;QAED,kEAAkE;QAClE,oGAAoG;QACpG,IAAI,UAAU;YAAE,OAAO,MAAM,CAAA;QAE7B,oEAAoE;QACpE,gGAAgG;QAChG,mGAAmG;QACnG,MAAM,4BAA4B,GAAG,QAAQ,CAAC,cAAc,CAAC,KAAK,CAC9D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CACxC,CAAA,CAAC,qDAAqD;QACvD,IACI,4BAA4B;YAC5B,CAAC,YAAY,IAAI,cAAc,IAAI,iBAAiB,CAAC;YAErD,OAAO,MAAM,CAAA;QAEjB,OAAO,SAAS,CAAA;IACpB,CAAC;IAED,6DAA6D;IACnD,gBAAgB,CACtB,UAAiB,EACjB,KAAY,EACZ,MAAqB,EACrB,QAAwB;QAExB,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;QAC5B,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAChD,KAAK,CAAC,IAAI,EACV,QAAQ,CACX,EAAE,CAAC;YACA,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAEzB,IAAI,KAAK,KAAK,SAAS;gBAAE,SAAQ;YACjC,2GAA2G;iBACtG,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB;gBAAE,OAAO,GAAG,IAAI,CAAA;YAEpE,MAAM,CAAC,cAAc,CACjB,MAAM,EACN,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAClD,CAAA;QACL,CAAC;QACD,OAAO,OAAO,CAAA;IAClB,CAAC;IAED;;OAEG;IACO,cAAc,CACpB,UAAiB,EACjB,MAAqB,EACrB,KAAY,EACZ,QAAwB;QAExB,IAAI,OAAO,GAAG,KAAK,CAAA;QAEnB,uCAAuC;QACvC,oCAAoC;QACpC,yHAAyH;QAEzH,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YACnD,qGAAqG;YAErG,8BAA8B;YAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAAE,SAAQ;YAE5B,iGAAiG;YACjG,IAAI,CAAC,IAAI,CAAC,UAAU;gBAAE,SAAQ;YAE9B,8IAA8I;YAC9I,6IAA6I;YAC7I,IACI,IAAI,CAAC,QAAQ;gBACb,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CACpB,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAC3C;gBAED,SAAQ;YAEZ,0DAA0D;YAC1D,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,IAAI,CAAC,wBAAwB,KAAK,KAAK,CAAC,IAAI;oBAAE,SAAQ;YAC9D,CAAC;iBAAM,CAAC;gBACJ,IACI,CAAC,IAAI,CAAC,QAAQ;oBACd,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI;oBAC/B,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,QAAS,CAAC,YAAY;oBAEzD,SAAQ;YAChB,CAAC;YAED,sCAAsC;YACtC,IAAI,MAAM,GAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YACxD,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;YAC1C,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,wGAAwG;YACtK,6CAA6C;YAC7C,IAAI,MAAM,KAAK,SAAS;gBAAE,SAAQ;YAElC,wEAAwE;YACxE,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,GAAG,MAAM,CAAA,CAAC,mBAAmB;YACvE,CAAC;iBAAM,CAAC;gBACJ,4BAA4B;gBAC5B,IAAI,CAAC,QAAS,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YACjD,CAAC;YAED,OAAO,GAAG,IAAI,CAAA;QAClB,CAAC;QACD,OAAO,OAAO,CAAA;IAClB,CAAC;IAES,oBAAoB,CAC1B,aAAoB,EACpB,KAAY,EACZ,MAAqB,EACrB,QAAwB;QAExB,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,KAAK,MAAM,CACP,KAAK,EACL,mBAAmB,EACtB,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,EAAE,CAAC;YACvC,IACI,mBAAmB,CAAC,mBAAmB,CAAC,WAAW;gBACnD,KAAK,CAAC,IAAI;gBAEV,SAAQ;YAEZ,MAAM,QAAQ,GAAG,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAA;YACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAC/C,QAAQ,EACR,mBAAmB,CAAC,mBAAmB,CAAC,WAAW,EACnD,aAAa,CAChB,CAAA;YACD,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC9C,SAAQ;YACZ,CAAC;YAED,oCAAoC;YACpC,IAAI,CAAC,kCAAkC,EAAE,CAAA;YAEzC,4CAA4C;YAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;YACnD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;YAErD,yBAAyB;YACzB,MAAM,UAAU,GACZ,mBAAmB,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,KAAK,CACnE,GAAG,CACN,CAAA;YACL,MAAM,aAAa,GAAG,CAClB,UAAoB,EACpB,GAAkB,EAClB,KAAU,EACP,EAAE;gBACL,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,EAAE,CAAA;gBACnC,IAAI,QAAQ,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtC,GAAG,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;oBACrB,OAAO,GAAG,CAAA;gBACd,CAAC;gBACD,IAAI,QAAQ,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpC,aAAa,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAA;gBACnD,CAAC;qBAAM,CAAC;oBACJ,OAAO,GAAG,CAAA;gBACd,CAAC;YACL,CAAC,CAAA;YACD,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAC9C,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;oBAC1B,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC5C,OAAO,GAAG,IAAI,CAAA;gBAClB,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;gBACzC,OAAO,GAAG,OAAO,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA;YAC1C,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAA;IAClB,CAAC;IAES,uBAAuB,CAC7B,aAAoB,EACpB,KAAY,EACZ,MAAqB;QAErB,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,KAAK,MAAM,sBAAsB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChE,IACI,sBAAsB,CAAC,sBAAsB,CAAC,WAAW;gBACzD,KAAK,CAAC,IAAI;gBAEV,SAAQ;YACZ,MAAM,QAAQ,GACV,sBAAsB,CAAC,sBAAsB,CAAC,QAAQ,CAAA;YAC1D,IAAI,mBAA2B,CAAA;YAE/B,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACvB,mBAAmB;oBACf,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAiB;yBACrD,YAAY,CAAA,CAAC,2BAA2B;YACrD,CAAC;iBAAM,CAAC;gBACJ,mBAAmB,GAAG,QAAQ,CAAC,QAAQ;oBACnC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAiB,CAAC,YAAY;oBACxD,CAAC,CAAC,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAiB;yBACrD,YAAY,CAAA;YAC3B,CAAC;YAED,MAAM,oBAAoB,GACtB,aAAa,CAAC,CAAC,CAAC,CACZ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC,CACnD,CAAA,CAAC,sGAAsG;YAC5G,IACI,oBAAoB,KAAK,SAAS;gBAClC,oBAAoB,KAAK,IAAI,EAC/B,CAAC;gBACC,MAAM,CACF,sBAAsB,CAAC,sBAAsB,CAAC,yBAAyB,CAC1E,GAAG,CAAC,CAAA;gBACL,KAAK,MAAM,MAAM,IAAI,sBAAsB,CAAC,OAAO,EAAE,CAAC;oBAClD,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,oBAAoB;wBAAE,SAAQ;oBACzD,MAAM,CACF,sBAAsB,CAAC,sBAAsB,CAAC,yBAAyB,CAC1E,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;oBAC3B,OAAO,GAAG,IAAI,CAAA;gBAClB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAA;IAClB,CAAC;IAEO,mBAAmB,CAAC,SAAiB,EAAE,QAAwB;QACnE,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAChD,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;YACrB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;QAC/C,CAAC;QACD,IAAI,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACrC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,GAAG,QAAQ,CAAC,OAAO;iBACrB,MAAM,CACH,CAAC,MAAM,EAAE,EAAE,CACP,CAAC,MAAM,CAAC,SAAS;gBACjB,qHAAqH;gBACrH,8DAA8D;gBAC9D,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,GAAG,SAAS,IAAI,MAAM,CAAC,YAAY,EAAE,CACxC,CAAC;gBACN,2EAA2E;gBAC3E,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAC/B,CAAC,aAAa,EAAE,EAAE,CACd,aAAa,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAC7C,CACR;iBACA,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC;gBACb,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,YAAY,CAAC;gBAC/C,MAAM;aACT,CAAC,CAAA;YACN,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpC,CAAC;QACD,OAAO,OAAO,CAAA;IAClB,CAAC;IAEO,6BAA6B,CACjC,QAA0B,EAC1B,WAAmB,EACnB,aAAoB;QAEpB,IAAI,OAAyB,CAAA;QAC7B,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YACnD,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,GAAG,CAChD,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAC7B,CAAA;QACL,CAAC;aAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAC7D,OAAO,GAAG,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,GAAG,CAC/C,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAC7B,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACpB,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;YAClE,CAAC;iBAAM,CAAC;gBACJ,OAAO,GAAG,QAAQ,CAAC,eAAgB,CAAC,kBAAkB,CAAC,GAAG,CACtD,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAC7B,CAAA;YACL,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;YACvC,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACvC,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;oBACnD,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC;wBACzB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAC5B,YAAY,CACR,IAAI,CAAC,UAAU,CACX,WAAW,EACX,MAAM,CAAC,YAAY,CACtB,CACJ,EACD,MAAM,CACT,CAAA;gBACT,CAAC;qBAAM,CAAC;oBACJ,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC;wBACzB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAC5B,YAAY,CACR,IAAI,CAAC,UAAU,CACX,WAAW,EACX,MAAM,CAAC,gBAAiB,CAAC,YAAY,CACxC,CACJ,EACD,MAAM,CAAC,gBAAiB,CAC3B,CAAA;gBACT,CAAC;YACL,CAAC;YACD,OAAO,QAAQ,CAAA;QACnB,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAEO,uBAAuB,CAC3B,QAA0B,EAC1B,mBAAwB;QAExB,IAAI,OAAyB,CAAA;QAC7B,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YACnD,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,GAAG,CAChD,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAC7B,CAAA;QACL,CAAC;aAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAC7D,OAAO,GAAG,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,GAAG,CAC/C,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAC7B,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACpB,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;YAClE,CAAC;iBAAM,CAAC;gBACJ,OAAO,GAAG,QAAQ,CAAC,eAAgB,CAAC,kBAAkB,CAAC,GAAG,CACtD,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAC7B,CAAA;YACL,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YACpE,OAAO,IAAI,CAAA;QACf,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAED;;;;;;OAMG;IAEH,6GAA6G;IACrG,kCAAkC;QACtC,6DAA6D;QAC7D,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAM;QACV,CAAC;QAED,mDAAmD;QACnD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAC/C,CAAC,mBAAmB,EAAE,EAAE;YACpB,MAAM,QAAQ,GACV,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAA;YAEpD,4BAA4B;YAC5B,IAAI,OAAyB,CAAA;YAC7B,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;gBACnD,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAA;YAClC,CAAC;iBAAM,IACH,QAAQ,CAAC,WAAW;gBACpB,QAAQ,CAAC,kBAAkB,EAC7B,CAAC;gBACC,OAAO,GAAG,QAAQ,CAAC,qBAAqB,CAAC,cAAc,CAAA;YAC3D,CAAC;iBAAM,CAAC;gBACJ,aAAa;gBACb,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACpB,OAAO,GAAG,QAAQ,CAAC,kBAAkB,CAAA;gBACzC,CAAC;qBAAM,CAAC;oBACJ,OAAO,GAAG,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAA;gBACnD,CAAC;YACL,CAAC;YAED,mDAAmD;YACnD,OAAO,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAEtC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACf,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;oBACzC,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;oBACvC,IACI,QAAQ,CAAC,WAAW;wBACpB,QAAQ,CAAC,kBAAkB,EAC7B,CAAC;wBACC,IACI,MAAM,CAAC,SAAS;4BAChB,MAAM,CAAC,gBAAgB;4BACvB,MAAM,CAAC,gBAAgB,CAAC,YAAY;gCAChC,MAAM,CAAC,YAAY,EACzB,CAAC;4BACC,0BAA0B;4BAC1B,KAAK;gCACD,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAClC,KAAK,CACR,CAAA;wBACT,CAAC;wBAED,OAAO,QAAQ,CAAC,SAAS,CACrB,KAAK,EACL,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAC/B,CAAA;oBACL,CAAC;oBACD,IACI,CAAC,MAAM,CAAC,SAAS;wBACjB,MAAM,CAAC,gBAAiB,CAAC,gBAAgB,EAC3C,CAAC;wBACC,0BAA0B;wBAC1B,KAAK;4BACD,MAAM,CAAC,gBAAiB,CAAC,gBAAgB,CAAC,cAAc,CACpD,KAAK,CACR,CAAA;oBACT,CAAC;oBAED,OAAO,QAAQ,CAAC,SAAS,CACrB,KAAK,EACL,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CACjD,CAAA;gBACL,CAAC,EAAE,EAAmB,CAAC,CAAA;gBAEvB,IACI,OAAO,CAAC,MAAM,KAAK,CAAC;oBACpB,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,eAAe,EAC1D,CAAC;oBACC,IACI,QAAQ,CAAC,WAAW;wBACpB,QAAQ,CAAC,kBAAkB,EAC7B,CAAC;wBACC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;oBAC5C,CAAC;yBAAM,CAAC;wBACJ,KAAK;4BACD,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAiB,CAAC,cAAc,CACvC,KAAK,CACR,CAAA;oBACT,CAAC;gBACL,CAAC;gBAED,wEAAwE;gBACxE,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;oBAEjD,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;wBACZ,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBACzB,CAAC;yBAAM,CAAC;wBACJ,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;oBACvB,CAAC;gBACL,CAAC;gBAED,OAAO,GAAG,CAAA;YACd,CAAC,EAAE,EAAE,CAAC,CAAA;QACV,CAAC,CACJ,CAAA;IACL,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,QAA0B,EAAE,IAAmB;QACjE,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACrE,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;IAC3C,CAAC;CACJ", "file": "RawSqlResultsToEntityTransformer.js", "sourcesContent": ["import { Driver } from \"../../driver/Driver\"\nimport { RelationIdLoadResult } from \"../relation-id/RelationIdLoadResult\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { <PERSON>as } from \"../Alias\"\nimport { RelationCountLoadResult } from \"../relation-count/RelationCountLoadResult\"\nimport { RelationMetadata } from \"../../metadata/RelationMetadata\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { QueryExpressionMap } from \"../QueryExpressionMap\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { QueryRunner } from \"../..\"\nimport { DriverUtils } from \"../../driver/DriverUtils\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\n\n/**\n * Transforms raw sql results returned from the database into entity object.\n * Entity is constructed based on its entity metadata.\n */\nexport class RawSqlResultsToEntityTransformer {\n    /**\n     * Contains a hashmap for every rawRelationIdResults given.\n     * In the hashmap you will find the idMaps of a result under the hash of this.hashEntityIds for the result.\n     */\n    private relationIdMaps: Array<{ [idHash: string]: any[] }>\n\n    private pojo: boolean\n    private selections: Set<string>\n    private aliasCache: Map<string, Map<string, string>>\n    private columnsCache: Map<\n        string,\n        Map<EntityMetadata, [string, ColumnMetadata][]>\n    >\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected expressionMap: QueryExpressionMap,\n        protected driver: Driver,\n        protected rawRelationIdResults: RelationIdLoadResult[],\n        protected rawRelationCountResults: RelationCountLoadResult[],\n        protected queryRunner?: QueryRunner,\n    ) {\n        this.pojo = this.expressionMap.options.includes(\"create-pojo\")\n        this.selections = new Set(\n            this.expressionMap.selects.map((s) => s.selection),\n        )\n        this.aliasCache = new Map()\n        this.columnsCache = new Map()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Since db returns a duplicated rows of the data where accuracies of the same object can be duplicated\n     * we need to group our result and we must have some unique id (primary key in our case)\n     */\n    transform(rawResults: any[], alias: Alias): any[] {\n        const group = this.group(rawResults, alias)\n        const entities: any[] = []\n        for (const results of group.values()) {\n            const entity = this.transformRawResultsGroup(results, alias)\n            if (entity !== undefined) entities.push(entity)\n        }\n        return entities\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Build an alias from a name and column name.\n     */\n    protected buildAlias(aliasName: string, columnName: string) {\n        let aliases = this.aliasCache.get(aliasName)\n        if (!aliases) {\n            aliases = new Map()\n            this.aliasCache.set(aliasName, aliases)\n        }\n        let columnAlias = aliases.get(columnName)\n        if (!columnAlias) {\n            columnAlias = DriverUtils.buildAlias(\n                this.driver,\n                undefined,\n                aliasName,\n                columnName,\n            )\n            aliases.set(columnName, columnAlias)\n        }\n        return columnAlias\n    }\n\n    /**\n     * Groups given raw results by ids of given alias.\n     */\n    protected group(rawResults: any[], alias: Alias): Map<string, any[]> {\n        const map = new Map()\n        const keys: string[] = []\n        if (alias.metadata.tableType === \"view\") {\n            keys.push(\n                ...alias.metadata.columns.map((column) =>\n                    this.buildAlias(alias.name, column.databaseName),\n                ),\n            )\n        } else {\n            keys.push(\n                ...alias.metadata.primaryColumns.map((column) =>\n                    this.buildAlias(alias.name, column.databaseName),\n                ),\n            )\n        }\n        for (const rawResult of rawResults) {\n            const id = keys\n                .map((key) => {\n                    const keyValue = rawResult[key]\n\n                    if (Buffer.isBuffer(keyValue)) {\n                        return keyValue.toString(\"hex\")\n                    }\n\n                    if (ObjectUtils.isObject(keyValue)) {\n                        return JSON.stringify(keyValue)\n                    }\n\n                    return keyValue\n                })\n                .join(\"_\") // todo: check partial\n\n            const items = map.get(id)\n            if (!items) {\n                map.set(id, [rawResult])\n            } else {\n                items.push(rawResult)\n            }\n        }\n        return map\n    }\n\n    /**\n     * Transforms set of data results into single entity.\n     */\n    protected transformRawResultsGroup(\n        rawResults: any[],\n        alias: Alias,\n    ): ObjectLiteral | undefined {\n        // let hasColumns = false; // , hasEmbeddedColumns = false, hasParentColumns = false, hasParentEmbeddedColumns = false;\n        let metadata = alias.metadata\n\n        if (metadata.discriminatorColumn) {\n            const discriminatorValues = rawResults.map(\n                (result) =>\n                    result[\n                        this.buildAlias(\n                            alias.name,\n                            alias.metadata.discriminatorColumn!.databaseName,\n                        )\n                    ],\n            )\n            const discriminatorMetadata = metadata.childEntityMetadatas.find(\n                (childEntityMetadata) => {\n                    return (\n                        typeof discriminatorValues.find(\n                            (value) =>\n                                value ===\n                                childEntityMetadata.discriminatorValue,\n                        ) !== \"undefined\"\n                    )\n                },\n            )\n            if (discriminatorMetadata) metadata = discriminatorMetadata\n        }\n        const entity: any = metadata.create(this.queryRunner, {\n            fromDeserializer: true,\n            pojo: this.pojo,\n        })\n\n        // get value from columns selections and put them into newly created entity\n        const hasColumns = this.transformColumns(\n            rawResults,\n            alias,\n            entity,\n            metadata,\n        )\n        const hasRelations = this.transformJoins(\n            rawResults,\n            entity,\n            alias,\n            metadata,\n        )\n        const hasRelationIds = this.transformRelationIds(\n            rawResults,\n            alias,\n            entity,\n            metadata,\n        )\n        const hasRelationCounts = this.transformRelationCounts(\n            rawResults,\n            alias,\n            entity,\n        )\n\n        // if we have at least one selected column then return this entity\n        // since entity must have at least primary columns to be really selected and transformed into entity\n        if (hasColumns) return entity\n\n        // if we don't have any selected column we should not return entity,\n        // except for the case when entity only contain a primary column as a relation to another entity\n        // in this case its absolutely possible our entity to not have any columns except a single relation\n        const hasOnlyVirtualPrimaryColumns = metadata.primaryColumns.every(\n            (column) => column.isVirtual === true,\n        ) // todo: create metadata.hasOnlyVirtualPrimaryColumns\n        if (\n            hasOnlyVirtualPrimaryColumns &&\n            (hasRelations || hasRelationIds || hasRelationCounts)\n        )\n            return entity\n\n        return undefined\n    }\n\n    // get value from columns selections and put them into object\n    protected transformColumns(\n        rawResults: any[],\n        alias: Alias,\n        entity: ObjectLiteral,\n        metadata: EntityMetadata,\n    ): boolean {\n        let hasData = false\n        const result = rawResults[0]\n        for (const [key, column] of this.getColumnsToProcess(\n            alias.name,\n            metadata,\n        )) {\n            const value = result[key]\n\n            if (value === undefined) continue\n            // we don't mark it as has data because if we will have all nulls in our object - we don't need such object\n            else if (value !== null && !column.isVirtualProperty) hasData = true\n\n            column.setEntityValue(\n                entity,\n                this.driver.prepareHydratedValue(value, column),\n            )\n        }\n        return hasData\n    }\n\n    /**\n     * Transforms joined entities in the given raw results by a given alias and stores to the given (parent) entity\n     */\n    protected transformJoins(\n        rawResults: any[],\n        entity: ObjectLiteral,\n        alias: Alias,\n        metadata: EntityMetadata,\n    ) {\n        let hasData = false\n\n        // let discriminatorValue: string = \"\";\n        // if (metadata.discriminatorColumn)\n        //     discriminatorValue = rawResults[0][this.buildAlias(alias.name, alias.metadata.discriminatorColumn!.databaseName)];\n\n        for (const join of this.expressionMap.joinAttributes) {\n            // todo: we have problem here - when inner joins are used without selects it still create empty array\n\n            // skip joins without metadata\n            if (!join.metadata) continue\n\n            // if simple left or inner join was performed without selection then we don't need to do anything\n            if (!join.isSelected) continue\n\n            // this check need to avoid setting properties than not belong to entity when single table inheritance used. (todo: check if we still need it)\n            // const metadata = metadata.childEntityMetadatas.find(childEntityMetadata => discriminatorValue === childEntityMetadata.discriminatorValue);\n            if (\n                join.relation &&\n                !metadata.relations.find(\n                    (relation) => relation === join.relation,\n                )\n            )\n                continue\n\n            // some checks to make sure this join is for current alias\n            if (join.mapToProperty) {\n                if (join.mapToPropertyParentAlias !== alias.name) continue\n            } else {\n                if (\n                    !join.relation ||\n                    join.parentAlias !== alias.name ||\n                    join.relationPropertyPath !== join.relation!.propertyPath\n                )\n                    continue\n            }\n\n            // transform joined data into entities\n            let result: any = this.transform(rawResults, join.alias)\n            result = !join.isMany ? result[0] : result\n            result = !join.isMany && result === undefined ? null : result // this is needed to make relations to return null when its joined but nothing was found in the database\n            // if nothing was joined then simply continue\n            if (result === undefined) continue\n\n            // if join was mapped to some property then save result to that property\n            if (join.mapToPropertyPropertyName) {\n                entity[join.mapToPropertyPropertyName] = result // todo: fix embeds\n            } else {\n                // otherwise set to relation\n                join.relation!.setEntityValue(entity, result)\n            }\n\n            hasData = true\n        }\n        return hasData\n    }\n\n    protected transformRelationIds(\n        rawSqlResults: any[],\n        alias: Alias,\n        entity: ObjectLiteral,\n        metadata: EntityMetadata,\n    ): boolean {\n        let hasData = false\n        for (const [\n            index,\n            rawRelationIdResult,\n        ] of this.rawRelationIdResults.entries()) {\n            if (\n                rawRelationIdResult.relationIdAttribute.parentAlias !==\n                alias.name\n            )\n                continue\n\n            const relation = rawRelationIdResult.relationIdAttribute.relation\n            const valueMap = this.createValueMapFromJoinColumns(\n                relation,\n                rawRelationIdResult.relationIdAttribute.parentAlias,\n                rawSqlResults,\n            )\n            if (valueMap === undefined || valueMap === null) {\n                continue\n            }\n\n            // prepare common data for this call\n            this.prepareDataForTransformRelationIds()\n\n            // Extract idMaps from prepared data by hash\n            const hash = this.hashEntityIds(relation, valueMap)\n            const idMaps = this.relationIdMaps[index][hash] || []\n\n            // Map data to properties\n            const properties =\n                rawRelationIdResult.relationIdAttribute.mapToPropertyPropertyPath.split(\n                    \".\",\n                )\n            const mapToProperty = (\n                properties: string[],\n                map: ObjectLiteral,\n                value: any,\n            ): any => {\n                const property = properties.shift()\n                if (property && properties.length === 0) {\n                    map[property] = value\n                    return map\n                }\n                if (property && properties.length > 0) {\n                    mapToProperty(properties, map[property], value)\n                } else {\n                    return map\n                }\n            }\n            if (relation.isOneToOne || relation.isManyToOne) {\n                if (idMaps[0] !== undefined) {\n                    mapToProperty(properties, entity, idMaps[0])\n                    hasData = true\n                }\n            } else {\n                mapToProperty(properties, entity, idMaps)\n                hasData = hasData || idMaps.length > 0\n            }\n        }\n\n        return hasData\n    }\n\n    protected transformRelationCounts(\n        rawSqlResults: any[],\n        alias: Alias,\n        entity: ObjectLiteral,\n    ): boolean {\n        let hasData = false\n        for (const rawRelationCountResult of this.rawRelationCountResults) {\n            if (\n                rawRelationCountResult.relationCountAttribute.parentAlias !==\n                alias.name\n            )\n                continue\n            const relation =\n                rawRelationCountResult.relationCountAttribute.relation\n            let referenceColumnName: string\n\n            if (relation.isOneToMany) {\n                referenceColumnName =\n                    relation.inverseRelation!.joinColumns[0].referencedColumn!\n                        .databaseName // todo: fix joinColumns[0]\n            } else {\n                referenceColumnName = relation.isOwning\n                    ? relation.joinColumns[0].referencedColumn!.databaseName\n                    : relation.inverseRelation!.joinColumns[0].referencedColumn!\n                          .databaseName\n            }\n\n            const referenceColumnValue =\n                rawSqlResults[0][\n                    this.buildAlias(alias.name, referenceColumnName)\n                ] // we use zero index since its grouped data // todo: selection with alias for entity columns wont work\n            if (\n                referenceColumnValue !== undefined &&\n                referenceColumnValue !== null\n            ) {\n                entity[\n                    rawRelationCountResult.relationCountAttribute.mapToPropertyPropertyName\n                ] = 0\n                for (const result of rawRelationCountResult.results) {\n                    if (result[\"parentId\"] !== referenceColumnValue) continue\n                    entity[\n                        rawRelationCountResult.relationCountAttribute.mapToPropertyPropertyName\n                    ] = parseInt(result[\"cnt\"])\n                    hasData = true\n                }\n            }\n        }\n\n        return hasData\n    }\n\n    private getColumnsToProcess(aliasName: string, metadata: EntityMetadata) {\n        let metadatas = this.columnsCache.get(aliasName)\n        if (!metadatas) {\n            metadatas = new Map()\n            this.columnsCache.set(aliasName, metadatas)\n        }\n        let columns = metadatas.get(metadata)\n        if (!columns) {\n            columns = metadata.columns\n                .filter(\n                    (column) =>\n                        !column.isVirtual &&\n                        // if user does not selected the whole entity or he used partial selection and does not select this particular column\n                        // then we don't add this column and its value into the entity\n                        (this.selections.has(aliasName) ||\n                            this.selections.has(\n                                `${aliasName}.${column.propertyPath}`,\n                            )) &&\n                        // if table inheritance is used make sure this column is not child's column\n                        !metadata.childEntityMetadatas.some(\n                            (childMetadata) =>\n                                childMetadata.target === column.target,\n                        ),\n                )\n                .map((column) => [\n                    this.buildAlias(aliasName, column.databaseName),\n                    column,\n                ])\n            metadatas.set(metadata, columns)\n        }\n        return columns\n    }\n\n    private createValueMapFromJoinColumns(\n        relation: RelationMetadata,\n        parentAlias: string,\n        rawSqlResults: any[],\n    ): ObjectLiteral {\n        let columns: ColumnMetadata[]\n        if (relation.isManyToOne || relation.isOneToOneOwner) {\n            columns = relation.entityMetadata.primaryColumns.map(\n                (joinColumn) => joinColumn,\n            )\n        } else if (relation.isOneToMany || relation.isOneToOneNotOwner) {\n            columns = relation.inverseRelation!.joinColumns.map(\n                (joinColumn) => joinColumn,\n            )\n        } else {\n            if (relation.isOwning) {\n                columns = relation.joinColumns.map((joinColumn) => joinColumn)\n            } else {\n                columns = relation.inverseRelation!.inverseJoinColumns.map(\n                    (joinColumn) => joinColumn,\n                )\n            }\n        }\n        return columns.reduce((valueMap, column) => {\n            for (const rawSqlResult of rawSqlResults) {\n                if (relation.isManyToOne || relation.isOneToOneOwner) {\n                    valueMap[column.databaseName] =\n                        this.driver.prepareHydratedValue(\n                            rawSqlResult[\n                                this.buildAlias(\n                                    parentAlias,\n                                    column.databaseName,\n                                )\n                            ],\n                            column,\n                        )\n                } else {\n                    valueMap[column.databaseName] =\n                        this.driver.prepareHydratedValue(\n                            rawSqlResult[\n                                this.buildAlias(\n                                    parentAlias,\n                                    column.referencedColumn!.databaseName,\n                                )\n                            ],\n                            column.referencedColumn!,\n                        )\n                }\n            }\n            return valueMap\n        }, {} as ObjectLiteral)\n    }\n\n    private extractEntityPrimaryIds(\n        relation: RelationMetadata,\n        relationIdRawResult: any,\n    ) {\n        let columns: ColumnMetadata[]\n        if (relation.isManyToOne || relation.isOneToOneOwner) {\n            columns = relation.entityMetadata.primaryColumns.map(\n                (joinColumn) => joinColumn,\n            )\n        } else if (relation.isOneToMany || relation.isOneToOneNotOwner) {\n            columns = relation.inverseRelation!.joinColumns.map(\n                (joinColumn) => joinColumn,\n            )\n        } else {\n            if (relation.isOwning) {\n                columns = relation.joinColumns.map((joinColumn) => joinColumn)\n            } else {\n                columns = relation.inverseRelation!.inverseJoinColumns.map(\n                    (joinColumn) => joinColumn,\n                )\n            }\n        }\n        return columns.reduce((data, column) => {\n            data[column.databaseName] = relationIdRawResult[column.databaseName]\n            return data\n        }, {} as ObjectLiteral)\n    }\n\n    /*private removeVirtualColumns(entity: ObjectLiteral, alias: Alias) {\n        const virtualColumns = this.expressionMap.selects\n            .filter(select => select.virtual)\n            .map(select => select.selection.replace(alias.name + \".\", \"\"));\n\n        virtualColumns.forEach(virtualColumn => delete entity[virtualColumn]);\n    }*/\n\n    /** Prepare data to run #transformRelationIds, as a lot of result independent data is needed in every call */\n    private prepareDataForTransformRelationIds() {\n        // Return early if the relationIdMaps were already calculated\n        if (this.relationIdMaps) {\n            return\n        }\n\n        // Ensure this prepare function is only called once\n        this.relationIdMaps = this.rawRelationIdResults.map(\n            (rawRelationIdResult) => {\n                const relation =\n                    rawRelationIdResult.relationIdAttribute.relation\n\n                // Calculate column metadata\n                let columns: ColumnMetadata[]\n                if (relation.isManyToOne || relation.isOneToOneOwner) {\n                    columns = relation.joinColumns\n                } else if (\n                    relation.isOneToMany ||\n                    relation.isOneToOneNotOwner\n                ) {\n                    columns = relation.inverseEntityMetadata.primaryColumns\n                } else {\n                    // ManyToMany\n                    if (relation.isOwning) {\n                        columns = relation.inverseJoinColumns\n                    } else {\n                        columns = relation.inverseRelation!.joinColumns\n                    }\n                }\n\n                // Calculate the idMaps for the rawRelationIdResult\n                return rawRelationIdResult.results.reduce<{\n                    [idHash: string]: any[]\n                }>((agg, result) => {\n                    let idMap = columns.reduce((idMap, column) => {\n                        let value = result[column.databaseName]\n                        if (\n                            relation.isOneToMany ||\n                            relation.isOneToOneNotOwner\n                        ) {\n                            if (\n                                column.isVirtual &&\n                                column.referencedColumn &&\n                                column.referencedColumn.propertyName !==\n                                    column.propertyName\n                            ) {\n                                // if column is a relation\n                                value =\n                                    column.referencedColumn.createValueMap(\n                                        value,\n                                    )\n                            }\n\n                            return OrmUtils.mergeDeep(\n                                idMap,\n                                column.createValueMap(value),\n                            )\n                        }\n                        if (\n                            !column.isPrimary &&\n                            column.referencedColumn!.referencedColumn\n                        ) {\n                            // if column is a relation\n                            value =\n                                column.referencedColumn!.referencedColumn.createValueMap(\n                                    value,\n                                )\n                        }\n\n                        return OrmUtils.mergeDeep(\n                            idMap,\n                            column.referencedColumn!.createValueMap(value),\n                        )\n                    }, {} as ObjectLiteral)\n\n                    if (\n                        columns.length === 1 &&\n                        !rawRelationIdResult.relationIdAttribute.disableMixedMap\n                    ) {\n                        if (\n                            relation.isOneToMany ||\n                            relation.isOneToOneNotOwner\n                        ) {\n                            idMap = columns[0].getEntityValue(idMap)\n                        } else {\n                            idMap =\n                                columns[0].referencedColumn!.getEntityValue(\n                                    idMap,\n                                )\n                        }\n                    }\n\n                    // If an idMap is found, set it in the aggregator under the correct hash\n                    if (idMap !== undefined) {\n                        const hash = this.hashEntityIds(relation, result)\n\n                        if (agg[hash]) {\n                            agg[hash].push(idMap)\n                        } else {\n                            agg[hash] = [idMap]\n                        }\n                    }\n\n                    return agg\n                }, {})\n            },\n        )\n    }\n\n    /**\n     * Use a simple JSON.stringify to create a simple hash of the primary ids of an entity.\n     * As this.extractEntityPrimaryIds always creates the primary id object in the same order, if the same relation is\n     * given, a simple JSON.stringify should be enough to get a unique hash per entity!\n     */\n    private hashEntityIds(relation: RelationMetadata, data: ObjectLiteral) {\n        const entityPrimaryIds = this.extractEntityPrimaryIds(relation, data)\n        return JSON.stringify(entityPrimaryIds)\n    }\n}\n"], "sourceRoot": "../.."}