{"version": 3, "sources": ["../../src/decorator/options/SpatialColumnOptions.ts"], "names": [], "mappings": "", "file": "SpatialColumnOptions.js", "sourcesContent": ["import { Geometry } from \"../../driver/types/GeoJsonTypes\"\n\n/**\n * Options for spatial columns.\n */\nexport interface SpatialColumnOptions {\n    /**\n     * Column type's feature type.\n     * Geometry, Point, Polygon, etc.\n     */\n    spatialFeatureType?: Geometry[\"type\"]\n\n    /**\n     * Column type's SRID.\n     * Spatial Reference ID or EPSG code.\n     */\n    srid?: number\n}\n"], "sourceRoot": "../.."}