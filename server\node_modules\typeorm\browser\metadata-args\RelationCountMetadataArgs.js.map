{"version": 3, "sources": ["../browser/src/metadata-args/RelationCountMetadataArgs.ts"], "names": [], "mappings": "", "file": "RelationCountMetadataArgs.js", "sourcesContent": ["import { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\n\n/**\n * Arguments for RelationCountMetadata class.\n */\nexport interface RelationCountMetadataArgs {\n    /**\n     * Class to which this decorator is applied.\n     */\n    readonly target: Function\n\n    /**\n     * Class's property name to which this decorator is applied.\n     */\n    readonly propertyName: string\n\n    /**\n     * Target's relation which it should count.\n     */\n    readonly relation: string | ((object: any) => any)\n\n    /**\n     * <PERSON>as of the joined (destination) table.\n     */\n    readonly alias?: string\n\n    /**\n     * Extra condition applied to \"ON\" section of join.\n     */\n    readonly queryBuilderFactory?: (\n        qb: SelectQueryBuilder<any>,\n    ) => SelectQueryBuilder<any>\n}\n"], "sourceRoot": ".."}