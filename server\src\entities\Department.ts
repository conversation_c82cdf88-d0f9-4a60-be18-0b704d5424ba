import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, OneToMany } from 'typeorm'
import { User } from './User'

@Entity('sys_departments')
export class Department {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ type: 'varchar', length: 100 })
  name: string

  @Column({ name: 'parent_id', type: 'bigint', default: 0 })
  parentId: number

  @Column({ name: 'sort_order', type: 'int', default: 0 })
  sortOrder: number

  @Column({ type: 'tinyint', default: 1 })
  status: number

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  // 关联关系
  @OneToMany(() => User, user => user.department)
  users: User[]

  // 虚拟字段
  children?: Department[]
}
