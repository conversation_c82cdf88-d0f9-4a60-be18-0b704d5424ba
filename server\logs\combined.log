{"level":"info","message":"Database connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:12:57"}
{"level":"info","message":"<PERSON><PERSON> connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:12:57"}
{"level":"info","message":"<PERSON><PERSON> is ready","service":"enterprise-app","timestamp":"2025-08-27 20:12:57"}
{"level":"info","message":"Red<PERSON> connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:12:57"}
{"level":"info","message":"Server is running on http://localhost:3000","service":"enterprise-app","timestamp":"2025-08-27 20:12:57"}
{"level":"info","message":"Environment: development","service":"enterprise-app","timestamp":"2025-08-27 20:12:57"}
{"level":"info","message":"::1 - - [27/Aug/2025:12:13:18 +0000] \"POST /api/auth/login HTTP/1.1\" 200 28 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"enterprise-app","timestamp":"2025-08-27 20:13:18"}
{"level":"info","message":"::1 - - [27/Aug/2025:12:13:54 +0000] \"GET /health HTTP/1.1\" 200 74 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"enterprise-app","timestamp":"2025-08-27 20:13:54"}
{"level":"info","message":"::1 - - [27/Aug/2025:12:13:54 +0000] \"GET /favicon.ico HTTP/1.1\" 404 49 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"enterprise-app","timestamp":"2025-08-27 20:13:54"}
{"level":"info","message":"::1 - - [27/Aug/2025:12:14:01 +0000] \"GET /api HTTP/1.1\" 200 28 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"enterprise-app","timestamp":"2025-08-27 20:14:01"}
{"level":"info","message":"::1 - - [27/Aug/2025:12:14:01 +0000] \"GET /favicon.ico HTTP/1.1\" 404 49 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"enterprise-app","timestamp":"2025-08-27 20:14:01"}
{"diagnosticCodes":[2307,2307,7006],"level":"error","message":"Failed to start server: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m3\u001b[0m:\u001b[93m22\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Role' or its corresponding type declarations.\r\n\r\n\u001b[7m3\u001b[0m import { Role } from './Role'\r\n\u001b[7m \u001b[0m \u001b[91m                     ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m39\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'role' implicitly has an 'any' type.\r\n\r\n\u001b[7m39\u001b[0m   @ManyToMany(() => Role, role => role.users)\r\n\u001b[7m  \u001b[0m \u001b[91m                          ~~~~\u001b[0m\r\n","service":"enterprise-app","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m3\u001b[0m:\u001b[93m22\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Role' or its corresponding type declarations.\r\n\r\n\u001b[7m3\u001b[0m import { Role } from './Role'\r\n\u001b[7m \u001b[0m \u001b[91m                     ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m39\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'role' implicitly has an 'any' type.\r\n\r\n\u001b[7m39\u001b[0m   @ManyToMany(() => Role, role => role.users)\r\n\u001b[7m  \u001b[0m \u001b[91m                          ~~~~\u001b[0m\r\n\n    at createTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1689:10\n    at Object.require.extensions.<computed> [as .ts] (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","timestamp":"2025-08-27 20:20:34"}
{"diagnosticCodes":[2307],"level":"error","message":"Failed to start server: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n","service":"enterprise-app","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1689:10\n    at Object.require.extensions.<computed> [as .ts] (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","timestamp":"2025-08-27 20:20:45"}
{"diagnosticCodes":[2307],"level":"error","message":"Failed to start server: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n","service":"enterprise-app","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1689:10\n    at Object.require.extensions.<computed> [as .ts] (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","timestamp":"2025-08-27 20:20:54"}
{"level":"info","message":"Database connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:04"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:04"}
{"level":"info","message":"Redis is ready","service":"enterprise-app","timestamp":"2025-08-27 20:21:04"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:04"}
{"level":"info","message":"Server is running on http://localhost:3000","service":"enterprise-app","timestamp":"2025-08-27 20:21:04"}
{"level":"info","message":"Environment: development","service":"enterprise-app","timestamp":"2025-08-27 20:21:04"}
{"level":"info","message":"Database connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:26"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:26"}
{"level":"info","message":"Redis is ready","service":"enterprise-app","timestamp":"2025-08-27 20:21:26"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:26"}
{"level":"info","message":"Server is running on http://localhost:3000","service":"enterprise-app","timestamp":"2025-08-27 20:21:26"}
{"level":"info","message":"Environment: development","service":"enterprise-app","timestamp":"2025-08-27 20:21:26"}
{"level":"info","message":"Database connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:41"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:41"}
{"level":"info","message":"Redis is ready","service":"enterprise-app","timestamp":"2025-08-27 20:21:41"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:21:41"}
{"level":"info","message":"Server is running on http://localhost:3000","service":"enterprise-app","timestamp":"2025-08-27 20:21:41"}
{"level":"info","message":"Environment: development","service":"enterprise-app","timestamp":"2025-08-27 20:21:41"}
{"level":"info","message":"Database connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:01"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:01"}
{"level":"info","message":"Redis is ready","service":"enterprise-app","timestamp":"2025-08-27 20:22:01"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:01"}
{"level":"info","message":"Server is running on http://localhost:3000","service":"enterprise-app","timestamp":"2025-08-27 20:22:01"}
{"level":"info","message":"Environment: development","service":"enterprise-app","timestamp":"2025-08-27 20:22:01"}
{"level":"info","message":"Database connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:10"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:10"}
{"level":"info","message":"Redis is ready","service":"enterprise-app","timestamp":"2025-08-27 20:22:10"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:10"}
{"level":"info","message":"Server is running on http://localhost:3000","service":"enterprise-app","timestamp":"2025-08-27 20:22:10"}
{"level":"info","message":"Environment: development","service":"enterprise-app","timestamp":"2025-08-27 20:22:10"}
{"level":"info","message":"Database connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:17"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:17"}
{"level":"info","message":"Redis is ready","service":"enterprise-app","timestamp":"2025-08-27 20:22:17"}
{"level":"info","message":"Redis connected successfully","service":"enterprise-app","timestamp":"2025-08-27 20:22:17"}
{"level":"info","message":"Server is running on http://localhost:3000","service":"enterprise-app","timestamp":"2025-08-27 20:22:17"}
{"level":"info","message":"Environment: development","service":"enterprise-app","timestamp":"2025-08-27 20:22:17"}
{"level":"info","message":"Database connected for data initialization","service":"enterprise-app","timestamp":"2025-08-27 20:25:28"}
{"level":"info","message":"Database initialization completed successfully","service":"enterprise-app","timestamp":"2025-08-27 20:25:28"}
{"level":"info","message":"Default users created:","service":"enterprise-app","timestamp":"2025-08-27 20:25:28"}
{"level":"info","message":"  - admin/123456 (超级管理员)","service":"enterprise-app","timestamp":"2025-08-27 20:25:28"}
{"level":"info","message":"  - test/123456 (普通用户)","service":"enterprise-app","timestamp":"2025-08-27 20:25:28"}
