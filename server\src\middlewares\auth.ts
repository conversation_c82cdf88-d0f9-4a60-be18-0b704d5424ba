import { Request, Response, NextFunction } from 'express'
import { AuthService } from '../services/AuthService'
import { logger } from '../utils/logger'

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: number
        username: string
        deptId?: number
        roles: string[]
        permissions: string[]
      }
    }
  }
}

const authService = new AuthService()

// JWT认证中间件
export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        code: 401,
        message: '访问令牌缺失'
      })
    }

    const userData = await authService.validateToken(token)
    req.user = userData

    next()
  } catch (error: any) {
    logger.error('Token validation failed:', error.message)
    return res.status(401).json({
      code: 401,
      message: '访问令牌无效或已过期'
    })
  }
}

// 权限验证中间件
export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '用户未认证'
      })
    }

    if (!req.user.permissions.includes(permission)) {
      return res.status(403).json({
        code: 403,
        message: '权限不足'
      })
    }

    next()
  }
}

// 角色验证中间件
export const requireRole = (role: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '用户未认证'
      })
    }

    if (!req.user.roles.includes(role)) {
      return res.status(403).json({
        code: 403,
        message: '角色权限不足'
      })
    }

    next()
  }
}

// 数据权限中间件
export const dataPermission = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      code: 401,
      message: '用户未认证'
    })
  }

  // 将数据权限信息添加到请求中，供后续查询使用
  req.dataScope = {
    userId: req.user.id,
    deptId: req.user.deptId,
    // 这里可以根据用户角色的dataScope字段来确定数据权限范围
    // 具体实现需要查询用户的角色信息
  }

  next()
}

// 扩展Request接口以包含数据权限信息
declare global {
  namespace Express {
    interface Request {
      dataScope?: {
        userId: number
        deptId?: number
        scope?: number
      }
    }
  }
}
