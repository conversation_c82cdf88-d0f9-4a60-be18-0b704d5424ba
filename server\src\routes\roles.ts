import { Router } from 'express'
import { RoleController } from '../controllers/RoleController'
import { authenticateToken, requirePermission } from '../middlewares/auth'

const router = Router()
const roleController = new RoleController()

// 所有角色管理路由都需要认证
router.use(authenticateToken)

// 获取角色列表
router.get('/', requirePermission('system:role:view'), roleController.getRoleList)

// 获取所有角色（用于下拉选择）
router.get('/all', requirePermission('system:role:view'), roleController.getAllRoles)

// 获取角色详情
router.get('/:id', requirePermission('system:role:view'), roleController.getRoleById)

// 创建角色
router.post('/', requirePermission('system:role:add'), roleController.createRole)

// 更新角色
router.put('/:id', requirePermission('system:role:edit'), roleController.updateRole)

// 删除角色
router.delete('/:id', requirePermission('system:role:delete'), roleController.deleteRole)

// 批量删除角色
router.post('/batch-delete', requirePermission('system:role:delete'), roleController.batchDeleteRoles)

// 分配菜单权限
router.post('/:id/assign-menus', requirePermission('system:role:edit'), roleController.assignMenus)

export default router
