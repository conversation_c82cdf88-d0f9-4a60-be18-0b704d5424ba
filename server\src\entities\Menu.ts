import { Entity, PrimaryGeneratedColumn, Column, ManyToMany } from 'typeorm'
import { Role } from './Role'

export enum MenuType {
  DIRECTORY = 1, // 目录
  MENU = 2,      // 菜单
  BUTTON = 3     // 按钮
}

@Entity('sys_menus')
export class Menu {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ type: 'varchar', length: 50 })
  name: string

  @Column({ name: 'parent_id', type: 'bigint', default: 0 })
  parentId: number

  @Column({ type: 'varchar', length: 200, nullable: true })
  path?: string

  @Column({ type: 'varchar', length: 200, nullable: true })
  component?: string

  @Column({ type: 'varchar', length: 100, nullable: true })
  permission?: string

  @Column({ type: 'tinyint', nullable: true })
  type?: MenuType

  @Column({ type: 'varchar', length: 50, nullable: true })
  icon?: string

  @Column({ name: 'sort_order', type: 'int', default: 0 })
  sortOrder: number

  @Column({ type: 'tinyint', default: 1 })
  status: number

  // 关联关系
  @ManyToMany(() => Role, role => role.menus)
  roles: Role[]

  // 虚拟字段
  children?: Menu[]
}
