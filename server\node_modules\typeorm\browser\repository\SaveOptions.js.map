{"version": 3, "sources": ["../browser/src/repository/SaveOptions.ts"], "names": [], "mappings": "", "file": "SaveOptions.js", "sourcesContent": ["/**\n * Special options passed to Repository#save, Repository#insert and Repository#update methods.\n */\nexport interface SaveOptions {\n    /**\n     * Additional data to be passed with persist method.\n     * This data can be used in subscribers then.\n     */\n    data?: any\n\n    /**\n     * Indicates if listeners and subscribers are called for this operation.\n     * By default they are enabled, you can disable them by setting { listeners: false } in save/remove options.\n     */\n    listeners?: boolean\n\n    /**\n     * By default transactions are enabled and all queries in persistence operation are wrapped into the transaction.\n     * You can disable this behaviour by setting { transaction: false } in the persistence options.\n     */\n    transaction?: boolean\n\n    /**\n     * Breaks save execution into chunks of a given size.\n     * For example, if you want to save 100,000 objects but you have issues with saving them,\n     * you can break them into 10 groups of 10,000 objects (by setting { chunk: 10000 }) and save each group separately.\n     * This option is needed to perform very big insertions when you have issues with underlying driver parameter number limitation.\n     */\n    chunk?: number\n\n    /**\n     * Flag to determine whether the entity that is being persisted\n     * should be reloaded during the persistence operation.\n     *\n     * It will work only on databases which do not support RETURNING / OUTPUT statement.\n     * Enabled by default.\n     */\n    reload?: boolean\n}\n"], "sourceRoot": ".."}