import { Router } from 'express'
import authRoutes from './auth'
import userRoutes from './users'
import roleRoutes from './roles'

const router = Router()

// 认证相关路由
router.use('/auth', authRoutes)

// 用户管理路由
router.use('/users', userRoutes)

// 角色管理路由
router.use('/roles', roleRoutes)

// 健康检查
router.get('/health', (req, res) => {
  res.json({
    code: 200,
    message: 'API服务运行正常',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

export default router
