import 'reflect-metadata'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import { config } from './config'
import { logger } from './utils/logger'
import { AppDataSource } from './config/database'
import redis from './config/redis'

// 创建Express应用
const app = express()

// 安全中间件
app.use(helmet())

// CORS配置
app.use(cors({
  origin: config.cors.origin,
  credentials: true
}))

// 压缩中间件
app.use(compression())

// 请求日志
app.use(morgan('combined', {
  stream: {
    write: (message: string) => {
      logger.info(message.trim())
    }
  }
}))

// 限流中间件
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    error: 'Too many requests from this IP, please try again later.'
  }
})
app.use('/api', limiter)

// 解析中间件
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 静态文件服务
app.use('/uploads', express.static(config.upload.dir))

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// API路由
import routes from './routes'
app.use('/api', routes)

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  })
})

// 错误处理中间件
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', err)

  res.status(err.status || 500).json({
    error: config.env === 'production' ? 'Internal server error' : err.message,
    ...(config.env !== 'production' && { stack: err.stack })
  })
})

// 启动服务器
async function bootstrap() {
  try {
    // 初始化数据库连接
    await AppDataSource.initialize()
    logger.info('Database connected successfully')

    // 测试Redis连接
    await redis.ping()
    logger.info('Redis connected successfully')

    // 启动HTTP服务器
    const server = app.listen(config.port, config.host, () => {
      logger.info(`Server is running on http://${config.host}:${config.port}`)
      logger.info(`Environment: ${config.env}`)
    })

    // 优雅关闭
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully`)

      server.close(async () => {
        logger.info('HTTP server closed')

        try {
          await AppDataSource.destroy()
          logger.info('Database connection closed')

          await redis.quit()
          logger.info('Redis connection closed')

          process.exit(0)
        } catch (error) {
          logger.error('Error during shutdown:', error)
          process.exit(1)
        }
      })
    }

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))

  } catch (error) {
    logger.error('Failed to start server:', error)
    process.exit(1)
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// 启动应用
bootstrap()
