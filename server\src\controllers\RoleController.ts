import { Request, Response } from 'express'
import Jo<PERSON> from 'joi'
import { RoleService } from '../services/RoleService'
import { logger } from '../utils/logger'

export class RoleController {
  private roleService: RoleService

  constructor() {
    this.roleService = new RoleService()
  }

  // 获取角色列表
  getRoleList = async (req: Request, res: Response) => {
    try {
      // 参数验证
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1),
        pageSize: Joi.number().integer().min(1).max(100).default(10),
        name: Joi.string().optional(),
        code: Joi.string().optional(),
        status: Joi.number().integer().valid(0, 1).optional()
      })

      const { error, value } = schema.validate(req.query)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const result = await this.roleService.getRoleList(value)

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      })
    } catch (error: any) {
      logger.error('Get role list error:', error.message)
      res.status(500).json({
        code: 500,
        message: error.message || '获取角色列表失败'
      })
    }
  }

  // 获取角色详情
  getRoleById = async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id)
      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '角色ID格式错误'
        })
      }

      const role = await this.roleService.getRoleById(id)

      res.json({
        code: 200,
        message: '获取成功',
        data: role
      })
    } catch (error: any) {
      logger.error('Get role by id error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '获取角色详情失败'
      })
    }
  }

  // 创建角色
  createRole = async (req: Request, res: Response) => {
    try {
      // 参数验证
      const schema = Joi.object({
        name: Joi.string().min(2).max(50).required().messages({
          'string.min': '角色名至少2个字符',
          'string.max': '角色名最多50个字符',
          'any.required': '角色名不能为空'
        }),
        code: Joi.string().min(2).max(50).required().messages({
          'string.min': '角色编码至少2个字符',
          'string.max': '角色编码最多50个字符',
          'any.required': '角色编码不能为空'
        }),
        description: Joi.string().max(200).optional(),
        dataScope: Joi.number().integer().valid(1, 2, 3, 4).default(4),
        menuIds: Joi.array().items(Joi.number().integer()).optional()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const role = await this.roleService.createRole(value)

      res.status(201).json({
        code: 201,
        message: '创建成功',
        data: role
      })
    } catch (error: any) {
      logger.error('Create role error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '创建角色失败'
      })
    }
  }

  // 更新角色
  updateRole = async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id)
      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '角色ID格式错误'
        })
      }

      // 参数验证
      const schema = Joi.object({
        name: Joi.string().min(2).max(50).optional(),
        code: Joi.string().min(2).max(50).optional(),
        description: Joi.string().max(200).optional(),
        dataScope: Joi.number().integer().valid(1, 2, 3, 4).optional(),
        status: Joi.number().integer().valid(0, 1).optional(),
        menuIds: Joi.array().items(Joi.number().integer()).optional()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const role = await this.roleService.updateRole({ id, ...value })

      res.json({
        code: 200,
        message: '更新成功',
        data: role
      })
    } catch (error: any) {
      logger.error('Update role error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '更新角色失败'
      })
    }
  }

  // 删除角色
  deleteRole = async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id)
      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '角色ID格式错误'
        })
      }

      await this.roleService.deleteRole(id)

      res.json({
        code: 200,
        message: '删除成功'
      })
    } catch (error: any) {
      logger.error('Delete role error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '删除角色失败'
      })
    }
  }

  // 获取所有角色
  getAllRoles = async (req: Request, res: Response) => {
    try {
      const roles = await this.roleService.getAllRoles()

      res.json({
        code: 200,
        message: '获取成功',
        data: roles
      })
    } catch (error: any) {
      logger.error('Get all roles error:', error.message)
      res.status(500).json({
        code: 500,
        message: error.message || '获取角色列表失败'
      })
    }
  }

  // 批量删除角色
  batchDeleteRoles = async (req: Request, res: Response) => {
    try {
      const schema = Joi.object({
        ids: Joi.array().items(Joi.number().integer()).min(1).required().messages({
          'array.min': '至少选择一个角色',
          'any.required': '角色ID列表不能为空'
        })
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      await this.roleService.batchDeleteRoles(value.ids)

      res.json({
        code: 200,
        message: '批量删除成功'
      })
    } catch (error: any) {
      logger.error('Batch delete roles error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '批量删除角色失败'
      })
    }
  }

  // 分配菜单权限
  assignMenus = async (req: Request, res: Response) => {
    try {
      const roleId = parseInt(req.params.id)
      if (isNaN(roleId)) {
        return res.status(400).json({
          code: 400,
          message: '角色ID格式错误'
        })
      }

      const schema = Joi.object({
        menuIds: Joi.array().items(Joi.number().integer()).required().messages({
          'any.required': '菜单ID列表不能为空'
        })
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const role = await this.roleService.assignMenus(roleId, value.menuIds)

      res.json({
        code: 200,
        message: '分配权限成功',
        data: role
      })
    } catch (error: any) {
      logger.error('Assign menus error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '分配权限失败'
      })
    }
  }
}
