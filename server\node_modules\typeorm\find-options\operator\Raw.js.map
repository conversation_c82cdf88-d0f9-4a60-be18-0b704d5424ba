{"version": 3, "sources": ["../../src/find-options/operator/Raw.ts"], "names": [], "mappings": ";;AA2BA,kBAgBC;AA3CD,kDAA8C;AA2B9C,SAAgB,GAAG,CACf,mBAA+D,EAC/D,sBAAsC;IAEtC,IAAI,OAAO,mBAAmB,KAAK,UAAU,EAAE,CAAC;QAC5C,OAAO,IAAI,2BAAY,CAAC,KAAK,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAA;IAC9D,CAAC;IAED,OAAO,IAAI,2BAAY,CACnB,KAAK,EACL,EAAE,EACF,IAAI,EACJ,IAAI,EACJ,mBAAmB,EACnB,sBAAsB,CACzB,CAAA;AACL,CAAC", "file": "Raw.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\n\n/**\n * Find Options Operator.\n * Example: { someField: Raw(\"12\") }\n */\nexport function Raw<T>(value: string): FindOperator<any>\n\n/**\n * Find Options Operator.\n * Example: { someField: Raw((columnAlias) => `${columnAlias} = 5`) }\n */\nexport function Raw<T>(\n    sqlGenerator: (columnAlias: string) => string,\n): FindOperator<any>\n\n/**\n * Find Options Operator.\n * For escaping parameters use next syntax:\n * Example: { someField: Raw((columnAlias) => `${columnAlias} = :value`, { value: 5 }) }\n */\nexport function Raw<T>(\n    sqlGenerator: (columnAlias: string) => string,\n    parameters: ObjectLiteral,\n): FindOperator<any>\n\nexport function Raw<T>(\n    valueOrSqlGenerator: string | ((columnAlias: string) => string),\n    sqlGeneratorParameters?: ObjectLiteral,\n): FindOperator<any> {\n    if (typeof valueOrSqlGenerator !== \"function\") {\n        return new FindOperator(\"raw\", valueOrSqlGenerator, false)\n    }\n\n    return new FindOperator(\n        \"raw\",\n        [],\n        true,\n        true,\n        valueOrSqlGenerator,\n        sqlGeneratorParameters,\n    )\n}\n"], "sourceRoot": "../.."}