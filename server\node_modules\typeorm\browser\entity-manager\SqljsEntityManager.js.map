{"version": 3, "sources": ["../browser/src/entity-manager/SqljsEntityManager.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAA;AAG/C;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,aAAa;IAKjD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB,EAAE,WAAyB;QACzD,KAAK,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QATzB,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;QAUrD,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAqB,CAAA;IAClD,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,KAAK,CAAC,YAAY,CACd,4BAAiD;QAEjD,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAA;IACxD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,YAAY,CAAC,sBAA+B;QAC9C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,cAAc;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;IAC/B,CAAC;CACJ", "file": "SqljsEntityManager.js", "sourcesContent": ["import { DataSource } from \"../data-source/DataSource\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { EntityManager } from \"./EntityManager\"\nimport { SqljsDriver } from \"../driver/sqljs/SqljsDriver\"\n\n/**\n * A special EntityManager that includes import/export and load/save function\n * that are unique to Sql.js.\n */\nexport class SqljsEntityManager extends EntityManager {\n    readonly \"@instanceof\" = Symbol.for(\"SqljsEntityManager\")\n\n    private driver: SqljsDriver\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource, queryRunner?: QueryRunner) {\n        super(connection, queryRunner)\n        this.driver = connection.driver as SqljsDriver\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads either the definition from a file (Node.js) or localstorage (browser)\n     * or uses the given definition to open a new database.\n     */\n    async loadDatabase(\n        fileNameOrLocalStorageOrData: string | Uint8Array,\n    ): Promise<void> {\n        await this.driver.load(fileNameOrLocalStorageOrData)\n    }\n\n    /**\n     * Saves the current database to a file (Node.js) or localstorage (browser)\n     * if fileNameOrLocalStorage is not set options.location is used.\n     */\n    async saveDatabase(fileNameOrLocalStorage?: string): Promise<void> {\n        await this.driver.save(fileNameOrLocalStorage)\n    }\n\n    /**\n     * Returns the current database definition.\n     */\n    exportDatabase(): Uint8Array {\n        return this.driver.export()\n    }\n}\n"], "sourceRoot": ".."}