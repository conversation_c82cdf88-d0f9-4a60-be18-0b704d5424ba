import { Router } from 'express'
import { AuthController } from '../controllers/AuthController'
import { authenticateToken } from '../middlewares/auth'

const router = Router()
const authController = new AuthController()

// 公开路由（不需要认证）
router.post('/login', authController.login)

// 需要认证的路由
router.use(authenticateToken)
router.get('/userinfo', authController.getUserInfo)
router.post('/logout', authController.logout)
router.post('/change-password', authController.changePassword)
router.post('/refresh', authController.refreshToken)

export default router
