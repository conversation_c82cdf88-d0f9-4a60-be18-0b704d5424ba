import { Router } from 'express'
import { UserController } from '../controllers/UserController'
import { authenticateToken, requirePermission } from '../middlewares/auth'

const router = Router()
const userController = new UserController()

// 所有用户管理路由都需要认证
router.use(authenticateToken)

// 获取用户列表
router.get('/', requirePermission('system:user:view'), userController.getUserList)

// 获取用户详情
router.get('/:id', requirePermission('system:user:view'), userController.getUserById)

// 创建用户
router.post('/', requirePermission('system:user:add'), userController.createUser)

// 更新用户
router.put('/:id', requirePermission('system:user:edit'), userController.updateUser)

// 删除用户
router.delete('/:id', requirePermission('system:user:delete'), userController.deleteUser)

// 重置密码
router.post('/:id/reset-password', requirePermission('system:user:edit'), userController.resetPassword)

// 批量删除用户
router.post('/batch-delete', requirePermission('system:user:delete'), userController.batchDeleteUsers)

export default router
