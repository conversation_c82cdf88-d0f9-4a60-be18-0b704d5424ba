import { Request, Response } from 'express'
import Jo<PERSON> from 'joi'
import { AuthService } from '../services/AuthService'
import { logger } from '../utils/logger'

export class AuthController {
  private authService: AuthService

  constructor() {
    this.authService = new AuthService()
  }

  // 用户登录
  login = async (req: Request, res: Response) => {
    try {
      // 参数验证
      const schema = Joi.object({
        username: Joi.string().min(2).max(50).required().messages({
          'string.min': '用户名至少2个字符',
          'string.max': '用户名最多50个字符',
          'any.required': '用户名不能为空'
        }),
        password: Joi.string().min(6).max(50).required().messages({
          'string.min': '密码至少6个字符',
          'string.max': '密码最多50个字符',
          'any.required': '密码不能为空'
        }),
        captcha: Joi.string().optional()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const { username, password } = value
      const result = await this.authService.login(username, password)

      res.json({
        code: 200,
        message: '登录成功',
        data: result
      })
    } catch (error: any) {
      logger.error('Login error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '登录失败'
      })
    }
  }

  // 获取用户信息
  getUserInfo = async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          message: '用户未认证'
        })
      }

      const permissions = await this.authService.getUserPermissions(req.user.id)
      const menus = await this.authService.getUserMenus(req.user.id)

      res.json({
        code: 200,
        message: '获取成功',
        data: {
          user: req.user,
          permissions,
          roles: req.user.roles,
          menus
        }
      })
    } catch (error: any) {
      logger.error('Get user info error:', error.message)
      res.status(500).json({
        code: 500,
        message: '获取用户信息失败'
      })
    }
  }

  // 用户登出
  logout = async (req: Request, res: Response) => {
    try {
      if (req.user) {
        await this.authService.logout(req.user.id)
      }

      res.json({
        code: 200,
        message: '登出成功'
      })
    } catch (error: any) {
      logger.error('Logout error:', error.message)
      res.status(500).json({
        code: 500,
        message: '登出失败'
      })
    }
  }

  // 修改密码
  changePassword = async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          message: '用户未认证'
        })
      }

      // 参数验证
      const schema = Joi.object({
        oldPassword: Joi.string().required().messages({
          'any.required': '原密码不能为空'
        }),
        newPassword: Joi.string().min(6).max(50).required().messages({
          'string.min': '新密码至少6个字符',
          'string.max': '新密码最多50个字符',
          'any.required': '新密码不能为空'
        }),
        confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
          'any.only': '确认密码与新密码不一致',
          'any.required': '确认密码不能为空'
        })
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const { oldPassword, newPassword } = value
      await this.authService.changePassword(req.user.id, oldPassword, newPassword)

      res.json({
        code: 200,
        message: '密码修改成功'
      })
    } catch (error: any) {
      logger.error('Change password error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '密码修改失败'
      })
    }
  }

  // 刷新Token
  refreshToken = async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          message: '用户未认证'
        })
      }

      // 重新生成Token
      const result = await this.authService.login(req.user.username, '')
      
      res.json({
        code: 200,
        message: 'Token刷新成功',
        data: {
          token: result.token
        }
      })
    } catch (error: any) {
      logger.error('Refresh token error:', error.message)
      res.status(500).json({
        code: 500,
        message: 'Token刷新失败'
      })
    }
  }
}
