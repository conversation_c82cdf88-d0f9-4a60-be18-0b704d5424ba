{"diagnosticCodes":[2307,2307,7006],"level":"error","message":"Failed to start server: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m3\u001b[0m:\u001b[93m22\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Role' or its corresponding type declarations.\r\n\r\n\u001b[7m3\u001b[0m import { Role } from './Role'\r\n\u001b[7m \u001b[0m \u001b[91m                     ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m39\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'role' implicitly has an 'any' type.\r\n\r\n\u001b[7m39\u001b[0m   @ManyToMany(() => Role, role => role.users)\r\n\u001b[7m  \u001b[0m \u001b[91m                          ~~~~\u001b[0m\r\n","service":"enterprise-app","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m3\u001b[0m:\u001b[93m22\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Role' or its corresponding type declarations.\r\n\r\n\u001b[7m3\u001b[0m import { Role } from './Role'\r\n\u001b[7m \u001b[0m \u001b[91m                     ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m39\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'role' implicitly has an 'any' type.\r\n\r\n\u001b[7m39\u001b[0m   @ManyToMany(() => Role, role => role.users)\r\n\u001b[7m  \u001b[0m \u001b[91m                          ~~~~\u001b[0m\r\n\n    at createTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1689:10\n    at Object.require.extensions.<computed> [as .ts] (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","timestamp":"2025-08-27 20:20:34"}
{"diagnosticCodes":[2307],"level":"error","message":"Failed to start server: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n","service":"enterprise-app","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1689:10\n    at Object.require.extensions.<computed> [as .ts] (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","timestamp":"2025-08-27 20:20:45"}
{"diagnosticCodes":[2307],"level":"error","message":"Failed to start server: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n","service":"enterprise-app","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/entities/User.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m28\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module './Department' or its corresponding type declarations.\r\n\r\n\u001b[7m2\u001b[0m import { Department } from './Department'\r\n\u001b[7m \u001b[0m \u001b[91m                           ~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1689:10\n    at Object.require.extensions.<computed> [as .ts] (D:\\migopsl\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","timestamp":"2025-08-27 20:20:54"}
