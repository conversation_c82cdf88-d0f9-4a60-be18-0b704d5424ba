import { Request, Response } from 'express'
import Jo<PERSON> from 'joi'
import { UserService } from '../services/UserService'
import { logger } from '../utils/logger'

export class UserController {
  private userService: UserService

  constructor() {
    this.userService = new UserService()
  }

  // 获取用户列表
  getUserList = async (req: Request, res: Response) => {
    try {
      // 参数验证
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1),
        pageSize: Joi.number().integer().min(1).max(100).default(10),
        username: Joi.string().optional(),
        email: Joi.string().email().optional(),
        phone: Joi.string().optional(),
        status: Joi.number().integer().valid(0, 1).optional(),
        deptId: Joi.number().integer().optional()
      })

      const { error, value } = schema.validate(req.query)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const result = await this.userService.getUserList(value)

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      })
    } catch (error: any) {
      logger.error('Get user list error:', error.message)
      res.status(500).json({
        code: 500,
        message: error.message || '获取用户列表失败'
      })
    }
  }

  // 获取用户详情
  getUserById = async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id)
      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '用户ID格式错误'
        })
      }

      const user = await this.userService.getUserById(id)

      res.json({
        code: 200,
        message: '获取成功',
        data: user
      })
    } catch (error: any) {
      logger.error('Get user by id error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '获取用户详情失败'
      })
    }
  }

  // 创建用户
  createUser = async (req: Request, res: Response) => {
    try {
      // 参数验证
      const schema = Joi.object({
        username: Joi.string().min(2).max(50).required().messages({
          'string.min': '用户名至少2个字符',
          'string.max': '用户名最多50个字符',
          'any.required': '用户名不能为空'
        }),
        password: Joi.string().min(6).max(50).required().messages({
          'string.min': '密码至少6个字符',
          'string.max': '密码最多50个字符',
          'any.required': '密码不能为空'
        }),
        email: Joi.string().email().optional().messages({
          'string.email': '邮箱格式不正确'
        }),
        phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional().messages({
          'string.pattern.base': '手机号格式不正确'
        }),
        deptId: Joi.number().integer().optional(),
        roleIds: Joi.array().items(Joi.number().integer()).optional()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const user = await this.userService.createUser(value)

      res.status(201).json({
        code: 201,
        message: '创建成功',
        data: user
      })
    } catch (error: any) {
      logger.error('Create user error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '创建用户失败'
      })
    }
  }

  // 更新用户
  updateUser = async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id)
      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '用户ID格式错误'
        })
      }

      // 参数验证
      const schema = Joi.object({
        username: Joi.string().min(2).max(50).optional(),
        email: Joi.string().email().optional(),
        phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional(),
        status: Joi.number().integer().valid(0, 1).optional(),
        deptId: Joi.number().integer().optional(),
        roleIds: Joi.array().items(Joi.number().integer()).optional()
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      const user = await this.userService.updateUser({ id, ...value })

      res.json({
        code: 200,
        message: '更新成功',
        data: user
      })
    } catch (error: any) {
      logger.error('Update user error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '更新用户失败'
      })
    }
  }

  // 删除用户
  deleteUser = async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id)
      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '用户ID格式错误'
        })
      }

      await this.userService.deleteUser(id)

      res.json({
        code: 200,
        message: '删除成功'
      })
    } catch (error: any) {
      logger.error('Delete user error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '删除用户失败'
      })
    }
  }

  // 重置密码
  resetPassword = async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id)
      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '用户ID格式错误'
        })
      }

      const result = await this.userService.resetPassword(id)

      res.json({
        code: 200,
        message: '密码重置成功',
        data: result
      })
    } catch (error: any) {
      logger.error('Reset password error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '重置密码失败'
      })
    }
  }

  // 批量删除用户
  batchDeleteUsers = async (req: Request, res: Response) => {
    try {
      const schema = Joi.object({
        ids: Joi.array().items(Joi.number().integer()).min(1).required().messages({
          'array.min': '至少选择一个用户',
          'any.required': '用户ID列表不能为空'
        })
      })

      const { error, value } = schema.validate(req.body)
      if (error) {
        return res.status(400).json({
          code: 400,
          message: error.details[0].message
        })
      }

      await this.userService.batchDeleteUsers(value.ids)

      res.json({
        code: 200,
        message: '批量删除成功'
      })
    } catch (error: any) {
      logger.error('Batch delete users error:', error.message)
      res.status(400).json({
        code: 400,
        message: error.message || '批量删除用户失败'
      })
    }
  }
}
