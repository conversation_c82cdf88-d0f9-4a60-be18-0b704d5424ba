{"version": 3, "sources": ["../browser/src/query-builder/RelationIdLoader.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AAGnD;;GAEG;AACH,MAAM,OAAO,gBAAgB;IACzB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACY,UAAsB,EACpB,WAAqC;QADvC,eAAU,GAAV,UAAU,CAAY;QACpB,gBAAW,GAAX,WAAW,CAA0B;IAChD,CAAC;IAEJ,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,IAAI,CACA,QAA0B,EAC1B,gBAAiD,EACjD,8BAAgE;QAEhE,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAC5C,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;QACxB,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,8BAA8B,CAAC;YACjE,CAAC,CAAC,8BAA8B;YAChC,CAAC,CAAC,8BAA8B;gBAChC,CAAC,CAAC,CAAC,8BAA8B,CAAC;gBAClC,CAAC,CAAC,SAAS,CAAA;QAEf,4CAA4C;QAC5C,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAA;QACtE,CAAC;aAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC,gCAAgC,CACxC,QAAQ,EACR,QAAQ,EACR,eAAe,CAClB,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,6DAA6D;YAC7D,OAAO,IAAI,CAAC,mCAAmC,CAC3C,QAAQ,EACR,QAAQ,EACR,eAAe,CAClB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iCAAiC,CAInC,QAA0B,EAC1B,kBAA6B,EAC7B,uBAAmC,EACnC,YAAsC;QAEtC,mDAAmD;QACnD,yDAAyD;QACzD,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,WAAW,CAAA;QAC5D,MAAM,QAAQ,GAAS,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;YACpD,CAAC,CAAC,kBAAkB;YACpB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAA;QAE1B,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC3B,uBAAuB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAC/D,QAAQ,EACR,kBAAkB,EAClB,IAAI,CAAC,WAAW,EAChB,YAAY,CACf,CAAA;YACD,IAAI,CAAC,uBAAuB,CAAC,MAAM;gBAC/B,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;oBAC7B,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;iBACnC,CAAC,CAAC,CAAA;QACX,CAAC;QACD,+FAA+F;QAC/F,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,IAAI,CAC/B,QAAQ,EACR,kBAAkB,EAClB,uBAAuB,CAC1B,CAAA;QACD,qCAAqC;QACrC,mEAAmE;QACnE,2CAA2C;QAE3C,MAAM,eAAe,GAAS,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC;YAChE,CAAC,CAAC,uBAAuB;YACzB,CAAC,CAAC,CAAC,uBAAwB,CAAC,CAAA;QAEhC,IAAI,OAAO,GAAqB,EAAE,EAC9B,cAAc,GAAqB,EAAE,CAAA;QACzC,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YAC7B,OAAO,GAAG,QAAQ,CAAC,sBAAuB,CAAC,cAAc,CAAC,GAAG,CACzD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAiB,CACvC,CAAA;YACD,cAAc,GAAG,QAAQ,CAAC,sBAAuB,CAAC,YAAY,CAAC,GAAG,CAC9D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAiB,CACvC,CAAA;QACL,CAAC;aAAM,IAAI,QAAQ,CAAC,oBAAoB,EAAE,CAAC;YACvC,OAAO,GAAG,QAAQ,CAAC,sBAAuB,CAAC,YAAY,CAAC,GAAG,CACvD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAiB,CACvC,CAAA;YACD,cAAc;gBACV,QAAQ,CAAC,sBAAuB,CAAC,cAAc,CAAC,GAAG,CAC/C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAiB,CACvC,CAAA;QACT,CAAC;aAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC1D,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAC9B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAiB,CACvC,CAAA;YACD,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAA;QAC3D,CAAC;aAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAC7D,OAAO,GAAG,QAAQ,CAAC,eAAgB,CAAC,cAAc,CAAC,cAAc,CAAA;YACjE,cAAc,GAAG,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,GAAG,CACtD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAiB,CACvC,CAAA;QACL,CAAC;aAAM,CAAC;QACR,CAAC;QAED,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAwC;gBAC/C,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;aACnC,CAAA;YAED,MAAM,iBAAiB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;gBACxD,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;oBACnC,OAAO,MAAM,CAAC,kBAAkB,CAC5B,MAAM,EACN,UAAU,CACN,MAAM,CAAC,cAAc,CAAC,IAAI;wBACtB,GAAG;wBACH,MAAM,CAAC,iBAAiB,CAC/B,CACJ,CAAA;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,iBAAiB,CAAC,MAAM;gBAAE,OAAO,KAAK,CAAA;YAE3C,eAAe,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;gBACtC,iBAAiB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACrC,MAAM,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;wBAClD,OAAO,MAAM,CAAC,kBAAkB,CAC5B,aAAa,EACb,UAAU,CACN,WAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,MAAM,CAAC,cAAc,CAAC,IAAI;4BACtB,GAAG;4BACH,QAAQ,CAAC,YAAY,CAAC,OAAO,CACzB,GAAG,EACH,GAAG,CACN;4BACD,GAAG;4BACH,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAC5C,CACJ,CACJ,CAAA;oBACL,CAAC,CAAC,CAAA;oBACF,IAAI,oBAAoB,EAAE,CAAC;wBACvB,IAAI,MAAM,EAAE,CAAC;4BACT,CAAC;4BAAC,KAAK,CAAC,OAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;wBAChD,CAAC;6BAAM,CAAC;4BACJ,KAAK,CAAC,OAAO,GAAG,aAAa,CAAA;wBACjC,CAAC;oBACL,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACF,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IAEH,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,iBAAiB,CACvB,QAA0B,EAC1B,QAAyB,EACzB,eAAiC;QAEjC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,sBAAuB,CAAA;QACzD,MAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAA;QACvC,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ;YAC7B,CAAC,CAAC,gBAAgB,CAAC,YAAY;YAC/B,CAAC,CAAC,gBAAgB,CAAC,cAAc,CAAA;QACrC,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ;YACpC,CAAC,CAAC,gBAAgB,CAAC,cAAc;YACjC,CAAC,CAAC,gBAAgB,CAAC,YAAY,CAAA;QACnC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAE/D,yCAAyC;QACzC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACvB,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CACrC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,IAAI;gBACxC,GAAG;gBACH,MAAM,CAAC,gBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAC9D,CAAA;YACD,EAAE,CAAC,SAAS,CAAC,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;QACnE,CAAC,CAAC,CAAA;QACF,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC9B,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CACrC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,IAAI;gBACxC,GAAG;gBACH,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;gBACvC,GAAG;gBACH,MAAM,CAAC,gBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAC9D,CAAA;YACD,EAAE,CAAC,SAAS,CAAC,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;QACnE,CAAC,CAAC,CAAA;QAEF,wCAAwC;QACxC,IAAI,UAAU,GAAG,EAAE,CAAA;QACnB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACnC,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CACtD,CAAA;YACD,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAC9B,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,CACvC,CAAA;YAED,IAAI,aAAa,EAAE,CAAC;gBAChB,UAAU,GAAG,GAAG,SAAS,IACrB,OAAO,CAAC,CAAC,CAAC,CAAC,YACf,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;YAChC,CAAC;iBAAM,CAAC;gBACJ,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;gBAClC,UAAU;oBACN,SAAS;wBACT,GAAG;wBACH,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY;wBACvB,mBAAmB,CAAA,CAAC,6BAA6B;YACzD,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,UAAU;gBACN,GAAG;oBACH,QAAQ;yBACH,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;wBACzB,OAAO,OAAO;6BACT,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;4BACZ,MAAM,SAAS,GACX,UAAU;gCACV,WAAW;gCACX,GAAG;gCACH,MAAM,CAAC,YAAY,CAAA;4BACvB,EAAE,CAAC,YAAY,CACX,SAAS,EACT,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,MAAM,CACT,CACJ,CAAA;4BACD,OAAO,CACH,SAAS;gCACT,GAAG;gCACH,MAAM,CAAC,YAAY;gCACnB,MAAM;gCACN,SAAS,CACZ,CAAA;wBACL,CAAC,CAAC;6BACD,IAAI,CAAC,OAAO,CAAC,CAAA;oBACtB,CAAC,CAAC;yBACD,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;yBACzC,IAAI,CAAC,MAAM,CAAC;oBACjB,GAAG,CAAA;QACX,CAAC;QAED,gDAAgD;QAChD,IAAI,UAAU,GAAG,EAAE,CAAA;QACnB,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAC1C,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAC7D,CAAA;gBACD,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAC9B,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,CACvC,CAAA;gBAED,IAAI,aAAa,EAAE,CAAC;oBAChB,UAAU,GAAG,GAAG,SAAS,IACrB,cAAc,CAAC,CAAC,CAAC,CAAC,YACtB,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;gBAChC,CAAC;qBAAM,CAAC;oBACJ,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;oBAClC,UAAU;wBACN,SAAS;4BACT,GAAG;4BACH,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY;4BAC9B,mBAAmB,CAAA,CAAC,6BAA6B;gBACzD,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,UAAU;oBACN,GAAG;wBACH,eAAe;6BACV,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;4BACzB,OAAO,cAAc;iCAChB,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gCACZ,MAAM,SAAS,GACX,UAAU;oCACV,WAAW;oCACX,GAAG;oCACH,MAAM,CAAC,YAAY,CAAA;gCACvB,EAAE,CAAC,YAAY,CACX,SAAS,EACT,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,MAAM,CACT,CACJ,CAAA;gCACD,OAAO,CACH,SAAS;oCACT,GAAG;oCACH,MAAM,CAAC,YAAY;oCACnB,MAAM;oCACN,SAAS,CACZ,CAAA;4BACL,CAAC,CAAC;iCACD,IAAI,CAAC,OAAO,CAAC,CAAA;wBACtB,CAAC,CAAC;6BACD,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;6BACzC,IAAI,CAAC,MAAM,CAAC;wBACjB,GAAG,CAAA;YACX,CAAC;QACL,CAAC;QAED,8CAA8C;QAC9C,qEAAqE;QACrE,EAAE;QACF,mBAAmB;QACnB,mDAAmD;QACnD,oDAAoD;QACpD,qCAAqC;QACrC,8BAA8B;QAC9B,wDAAwD;QACxD,iDAAiD;QACjD,2CAA2C;QAC3C,kCAAkC;QAClC,8BAA8B;QAC9B,EAAE;QACF,WAAW;QACX,8BAA8B;QAC9B,IAAI;QAEJ,gBAAgB;QAChB,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC;aACrC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;aAC3B,IAAI,CAAC,OAAO,CAAC,CAAA;QAClB,OAAO,EAAE;aACJ,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC;aACxC,KAAK,CAAC,SAAS,CAAC;aAChB,UAAU,EAAE,CAAA;IACrB,CAAC;IAED;;OAEG;IACO,gCAAgC,CACtC,QAA0B,EAC1B,QAAyB,EACzB,eAAiC;QAEjC,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAA;QAEpD,sCAAsC;QACtC,oDAAoD;QACpD,MAAM,yBAAyB,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CACxD,CAAC,UAAU,EAAE,EAAE;YACX,OAAO,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CACnD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,UAAU,CACpC,CAAA;QACL,CAAC,CACJ,CAAA;QACD,IAAI,eAAe,IAAI,yBAAyB,EAAE,CAAC;YAC/C,MAAM,cAAc,GAAoB,EAAE,CAAA;YAC1C,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACxB,MAAM,aAAa,GAAkB,EAAE,CAAA;gBACvC,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAC1C,CAAC,aAAa,EAAE,EAAE;oBACd,MAAM,GAAG,GACL,aAAa,CAAC,cAAc,CAAC,IAAI;wBACjC,GAAG;wBACH,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;oBAChD,aAAa,CAAC,GAAG,CAAC;wBACd,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBAC5C,CAAC,CACJ,CAAA;gBAED,eAAe,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;oBACtC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;wBACxC,MAAM,iBAAiB,GACnB,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;wBACrC,MAAM,wBAAwB,GAC1B,UAAU,CAAC,gBAAiB,CAAC,cAAc,CACvC,aAAa,CAChB,CAAA;wBACL,IACI,iBAAiB,KAAK,SAAS;4BAC/B,wBAAwB,KAAK,SAAS;4BAEtC,OAAM;wBAEV,IAAI,iBAAiB,KAAK,wBAAwB,EAAE,CAAC;4BACjD,MAAM,GAAG,GACL,UAAU,CAAC,gBAAiB,CAAC,cAAc;iCACtC,IAAI;gCACT,GAAG;gCACH,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;gCACvC,GAAG;gCACH,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAAC,OAAO,CAC7C,GAAG,EACH,GAAG,CACN,CAAA;4BACL,aAAa,CAAC,GAAG,CAAC,GAAG,wBAAwB,CAAA;wBACjD,CAAC;oBACL,CAAC,CAAC,CAAA;gBACN,CAAC,CAAC,CAAA;gBACF,IACI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM;oBACjC,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM;wBACzC,QAAQ,CAAC,WAAW,CAAC,MAAM,EACjC,CAAC;oBACC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBACtC,CAAC;YACL,CAAC,CAAC,CAAA;YACF,gDAAgD;YAChD,mDAAmD;YACnD,IAAI,cAAc,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM;gBACzC,OAAO,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;QAC9C,CAAC;QAED,6BAA6B;QAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC/D,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YAC7D,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CACrC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,aAAa,CAAC,cAAc,CAAC,IAAI;gBAC7B,GAAG;gBACH,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CACnD,CAAA;YACD,EAAE,CAAC,SAAS,CACR,SAAS,GAAG,GAAG,GAAG,aAAa,CAAC,YAAY,EAC5C,UAAU,CACb,CAAA;QACL,CAAC,CAAC,CAAA;QACF,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CACrC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,IAAI;gBACxC,GAAG;gBACH,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;gBACvC,GAAG;gBACH,MAAM,CAAC,gBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAC9D,CAAA;YACD,EAAE,CAAC,SAAS,CAAC,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;QACnE,CAAC,CAAC,CAAA;QAEF,6BAA6B;QAC7B,IAAI,SAAS,GAAW,EAAE,CAAA;QAC1B,IAAI,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACnC,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CACpD,MAAM,CACT,CACJ,CAAA;YACD,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAC9B,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,CACvC,CAAA;YAED,IAAI,aAAa,EAAE,CAAC;gBAChB,SAAS,GAAG,GAAG,SAAS,IACpB,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAC9C,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;YAChC,CAAC;iBAAM,CAAC;gBACJ,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;gBACjC,SAAS;oBACL,SAAS;wBACT,GAAG;wBACH,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY;wBACtD,kBAAkB,CAAA,CAAC,6BAA6B;YACxD,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,QAAQ;iBACf,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACzB,OAAO,QAAQ,CAAC,cAAc,CAAC,cAAc;qBACxC,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBACzB,MAAM,SAAS,GACX,QAAQ,GAAG,WAAW,GAAG,GAAG,GAAG,WAAW,CAAA;oBAC9C,EAAE,CAAC,YAAY,CACX,SAAS,EACT,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAChC,CAAA;oBACD,OAAO,CACH,SAAS;wBACT,GAAG;wBACH,MAAM,CAAC,YAAY;wBACnB,MAAM;wBACN,SAAS,CACZ,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YACtB,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;iBACzC,IAAI,CAAC,MAAM,CAAC,CAAA;QACrB,CAAC;QAED,gBAAgB;QAChB,OAAO,EAAE;aACJ,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC;aAC/C,KAAK,CAAC,SAAS,CAAC;aAChB,UAAU,EAAE,CAAA;IACrB,CAAC;IAED;;OAEG;IACO,mCAAmC,CACzC,QAA0B,EAC1B,QAAyB,EACzB,eAAiC;QAEjC,MAAM,gBAAgB,GAAG,QAAQ,CAAA;QACjC,QAAQ,GAAG,QAAQ,CAAC,eAAgB,CAAA;QAEpC,IACI,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM;YAC7C,QAAQ,CAAC,WAAW,CAAC,MAAM,EAC7B,CAAC;YACC,MAAM,qBAAqB,GACvB,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;gBACpD,OAAO,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;YACtD,CAAC,CAAC,CAAA;YACN,IAAI,qBAAqB,EAAE,CAAC;gBACxB,OAAO,OAAO,CAAC,OAAO,CAClB,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;oBACpB,MAAM,MAAM,GAAkB,EAAE,CAAA;oBAChC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,UAAU;wBAC7C,MAAM,KAAK,GACP,UAAU,CAAC,gBAAiB,CAAC,cAAc,CACvC,MAAM,CACT,CAAA;wBACL,MAAM,cAAc,GAChB,UAAU,CAAC,gBAAiB,CAAC,cAAc;6BACtC,IAAI;4BACT,GAAG;4BACH,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAAC,OAAO,CAC7C,GAAG,EACH,GAAG,CACN,CAAA;wBACL,MAAM,iBAAiB,GACnB,UAAU,CAAC,cAAc,CAAC,IAAI;4BAC9B,GAAG;4BACH,gBAAgB,CAAC,YAAY,CAAC,OAAO,CACjC,GAAG,EACH,GAAG,CACN;4BACD,GAAG;4BACH,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;wBAC7C,MAAM,CAAC,cAAc,CAAC,GAAG,KAAK,CAAA;wBAC9B,MAAM,CAAC,iBAAiB,CAAC,GAAG,KAAK,CAAA;oBACrC,CAAC,CAAC,CAAA;oBACF,OAAO,MAAM,CAAA;gBACjB,CAAC,CAAC,CACL,CAAA;YACL,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAA;QAEpD,6BAA6B;QAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC/D,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YAC7D,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CACrC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,aAAa,CAAC,cAAc,CAAC,IAAI;gBAC7B,GAAG;gBACH,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC/C,GAAG;gBACH,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CACnD,CAAA;YACD,EAAE,CAAC,SAAS,CACR,SAAS,GAAG,GAAG,GAAG,aAAa,CAAC,YAAY,EAC5C,UAAU,CACb,CAAA;QACL,CAAC,CAAC,CAAA;QACF,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CACrC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,IAAI;gBACxC,GAAG;gBACH,MAAM,CAAC,gBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAC9D,CAAA;YACD,EAAE,CAAC,SAAS,CAAC,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;QACnE,CAAC,CAAC,CAAA;QAEF,6BAA6B;QAC7B,IAAI,SAAS,GAAW,EAAE,CAAA;QAC1B,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACnC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAiB,CAAC,cAAc,CACpD,MAAM,CACT,CACJ,CAAA;YACD,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAC9B,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,CACvC,CAAA;YAED,IAAI,aAAa,EAAE,CAAC;gBAChB,SAAS,GAAG,GAAG,SAAS,IACpB,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,YAC5B,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;YAChC,CAAC;iBAAM,CAAC;gBACJ,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;gBACjC,SAAS;oBACL,SAAS;wBACT,GAAG;wBACH,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,YAAY;wBACpC,kBAAkB,CAAA,CAAC,6BAA6B;YACxD,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,QAAQ;iBACf,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACzB,OAAO,QAAQ,CAAC,WAAW;qBACtB,GAAG,CAAC,CAAC,UAAU,EAAE,eAAe,EAAE,EAAE;oBACjC,MAAM,SAAS,GACX,QAAQ,GAAG,WAAW,GAAG,GAAG,GAAG,eAAe,CAAA;oBAClD,EAAE,CAAC,YAAY,CACX,SAAS,EACT,UAAU,CAAC,gBAAiB,CAAC,cAAc,CACvC,MAAM,CACT,CACJ,CAAA;oBACD,OAAO,CACH,SAAS;wBACT,GAAG;wBACH,UAAU,CAAC,YAAY;wBACvB,MAAM;wBACN,SAAS,CACZ,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YACtB,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;iBACzC,IAAI,CAAC,MAAM,CAAC,CAAA;QACrB,CAAC;QAED,gBAAgB;QAChB,OAAO,EAAE;aACJ,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC;aAC/C,KAAK,CAAC,SAAS,CAAC;aAChB,UAAU,EAAE,CAAA;IACrB,CAAC;CACJ", "file": "RelationIdLoader.js", "sourcesContent": ["import { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { SelectQueryBuilder } from \"./SelectQueryBuilder\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Loads relation ids for the given entities.\n */\nexport class RelationIdLoader {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        private connection: DataSource,\n        protected queryRunner?: QueryRunner | undefined,\n    ) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads relation ids of the given entity or entities.\n     */\n    load(\n        relation: RelationMetadata,\n        entityOrEntities: ObjectLiteral | ObjectLiteral[],\n        relatedEntityOrRelatedEntities?: ObjectLiteral | ObjectLiteral[],\n    ): Promise<any[]> {\n        const entities = Array.isArray(entityOrEntities)\n            ? entityOrEntities\n            : [entityOrEntities]\n        const relatedEntities = Array.isArray(relatedEntityOrRelatedEntities)\n            ? relatedEntityOrRelatedEntities\n            : relatedEntityOrRelatedEntities\n            ? [relatedEntityOrRelatedEntities]\n            : undefined\n\n        // load relation ids depend of relation type\n        if (relation.isManyToMany) {\n            return this.loadForManyToMany(relation, entities, relatedEntities)\n        } else if (relation.isManyToOne || relation.isOneToOneOwner) {\n            return this.loadForManyToOneAndOneToOneOwner(\n                relation,\n                entities,\n                relatedEntities,\n            )\n        } else {\n            // if (relation.isOneToMany || relation.isOneToOneNotOwner) {\n            return this.loadForOneToManyAndOneToOneNotOwner(\n                relation,\n                entities,\n                relatedEntities,\n            )\n        }\n    }\n\n    /**\n     * Loads relation ids of the given entities and groups them into the object with parent and children.\n     *\n     * todo: extract this method?\n     */\n    async loadManyToManyRelationIdsAndGroup<\n        E1 extends ObjectLiteral,\n        E2 extends ObjectLiteral,\n    >(\n        relation: RelationMetadata,\n        entitiesOrEntities: E1 | E1[],\n        relatedEntityOrEntities?: E2 | E2[],\n        queryBuilder?: SelectQueryBuilder<any>,\n    ): Promise<{ entity: E1; related?: E2 | E2[] }[]> {\n        // console.log(\"relation:\", relation.propertyName);\n        // console.log(\"entitiesOrEntities\", entitiesOrEntities);\n        const isMany = relation.isManyToMany || relation.isOneToMany\n        const entities: E1[] = Array.isArray(entitiesOrEntities)\n            ? entitiesOrEntities\n            : [entitiesOrEntities]\n\n        if (!relatedEntityOrEntities) {\n            relatedEntityOrEntities = await this.connection.relationLoader.load(\n                relation,\n                entitiesOrEntities,\n                this.queryRunner,\n                queryBuilder,\n            )\n            if (!relatedEntityOrEntities.length)\n                return entities.map((entity) => ({\n                    entity: entity,\n                    related: isMany ? [] : undefined,\n                }))\n        }\n        // const relationIds = await this.load(relation, relatedEntityOrEntities!, entitiesOrEntities);\n        const relationIds = await this.load(\n            relation,\n            entitiesOrEntities,\n            relatedEntityOrEntities,\n        )\n        // console.log(\"entities\", entities);\n        // console.log(\"relatedEntityOrEntities\", relatedEntityOrEntities);\n        // console.log(\"relationIds\", relationIds);\n\n        const relatedEntities: E2[] = Array.isArray(relatedEntityOrEntities)\n            ? relatedEntityOrEntities\n            : [relatedEntityOrEntities!]\n\n        let columns: ColumnMetadata[] = [],\n            inverseColumns: ColumnMetadata[] = []\n        if (relation.isManyToManyOwner) {\n            columns = relation.junctionEntityMetadata!.inverseColumns.map(\n                (column) => column.referencedColumn!,\n            )\n            inverseColumns = relation.junctionEntityMetadata!.ownerColumns.map(\n                (column) => column.referencedColumn!,\n            )\n        } else if (relation.isManyToManyNotOwner) {\n            columns = relation.junctionEntityMetadata!.ownerColumns.map(\n                (column) => column.referencedColumn!,\n            )\n            inverseColumns =\n                relation.junctionEntityMetadata!.inverseColumns.map(\n                    (column) => column.referencedColumn!,\n                )\n        } else if (relation.isManyToOne || relation.isOneToOneOwner) {\n            columns = relation.joinColumns.map(\n                (column) => column.referencedColumn!,\n            )\n            inverseColumns = relation.entityMetadata.primaryColumns\n        } else if (relation.isOneToMany || relation.isOneToOneNotOwner) {\n            columns = relation.inverseRelation!.entityMetadata.primaryColumns\n            inverseColumns = relation.inverseRelation!.joinColumns.map(\n                (column) => column.referencedColumn!,\n            )\n        } else {\n        }\n\n        return entities.map((entity) => {\n            const group: { entity: E1; related?: E2 | E2[] } = {\n                entity: entity,\n                related: isMany ? [] : undefined,\n            }\n\n            const entityRelationIds = relationIds.filter((relationId) => {\n                return inverseColumns.every((column) => {\n                    return column.compareEntityValue(\n                        entity,\n                        relationId[\n                            column.entityMetadata.name +\n                                \"_\" +\n                                column.propertyAliasName\n                        ],\n                    )\n                })\n            })\n            if (!entityRelationIds.length) return group\n\n            relatedEntities.forEach((relatedEntity) => {\n                entityRelationIds.forEach((relationId) => {\n                    const relatedEntityMatched = columns.every((column) => {\n                        return column.compareEntityValue(\n                            relatedEntity,\n                            relationId[\n                                DriverUtils.buildAlias(\n                                    this.connection.driver,\n                                    undefined,\n                                    column.entityMetadata.name +\n                                        \"_\" +\n                                        relation.propertyPath.replace(\n                                            \".\",\n                                            \"_\",\n                                        ) +\n                                        \"_\" +\n                                        column.propertyPath.replace(\".\", \"_\"),\n                                )\n                            ],\n                        )\n                    })\n                    if (relatedEntityMatched) {\n                        if (isMany) {\n                            ;(group.related as E2[]).push(relatedEntity)\n                        } else {\n                            group.related = relatedEntity\n                        }\n                    }\n                })\n            })\n            return group\n        })\n    }\n\n    /**\n     * Loads relation ids of the given entities and maps them into the given entity property.\n     async loadManyToManyRelationIdsAndMap(\n     relation: RelationMetadata,\n     entityOrEntities: ObjectLiteral|ObjectLiteral[],\n     mapToEntityOrEntities: ObjectLiteral|ObjectLiteral[],\n     propertyName: string\n     ): Promise<void> {\n        const relationIds = await this.loadManyToManyRelationIds(relation, entityOrEntities, mapToEntityOrEntities);\n        const mapToEntities = mapToEntityOrEntities instanceof Array ? mapToEntityOrEntities : [mapToEntityOrEntities];\n        const junctionMetadata = relation.junctionEntityMetadata!;\n        const mainAlias = junctionMetadata.name;\n        const columns = relation.isOwning ? junctionMetadata.inverseColumns : junctionMetadata.ownerColumns;\n        const inverseColumns = relation.isOwning ? junctionMetadata.ownerColumns : junctionMetadata.inverseColumns;\n        mapToEntities.forEach(mapToEntity => {\n            mapToEntity[propertyName] = [];\n            relationIds.forEach(relationId => {\n                const match = inverseColumns.every(column => {\n                    return column.referencedColumn!.getEntityValue(mapToEntity) === relationId[mainAlias + \"_\" + column.propertyName];\n                });\n                if (match) {\n                    if (columns.length === 1) {\n                        mapToEntity[propertyName].push(relationId[mainAlias + \"_\" + columns[0].propertyName]);\n                    } else {\n                        const value = {};\n                        columns.forEach(column => {\n                            column.referencedColumn!.setEntityValue(value, relationId[mainAlias + \"_\" + column.propertyName]);\n                        });\n                        mapToEntity[propertyName].push(value);\n                    }\n                }\n            });\n        });\n    }*/\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads relation ids for the many-to-many relation.\n     */\n    protected loadForManyToMany(\n        relation: RelationMetadata,\n        entities: ObjectLiteral[],\n        relatedEntities?: ObjectLiteral[],\n    ) {\n        const junctionMetadata = relation.junctionEntityMetadata!\n        const mainAlias = junctionMetadata.name\n        const columns = relation.isOwning\n            ? junctionMetadata.ownerColumns\n            : junctionMetadata.inverseColumns\n        const inverseColumns = relation.isOwning\n            ? junctionMetadata.inverseColumns\n            : junctionMetadata.ownerColumns\n        const qb = this.connection.createQueryBuilder(this.queryRunner)\n\n        // select all columns from junction table\n        columns.forEach((column) => {\n            const columnName = DriverUtils.buildAlias(\n                this.connection.driver,\n                undefined,\n                column.referencedColumn!.entityMetadata.name +\n                    \"_\" +\n                    column.referencedColumn!.propertyPath.replace(\".\", \"_\"),\n            )\n            qb.addSelect(mainAlias + \".\" + column.propertyPath, columnName)\n        })\n        inverseColumns.forEach((column) => {\n            const columnName = DriverUtils.buildAlias(\n                this.connection.driver,\n                undefined,\n                column.referencedColumn!.entityMetadata.name +\n                    \"_\" +\n                    relation.propertyPath.replace(\".\", \"_\") +\n                    \"_\" +\n                    column.referencedColumn!.propertyPath.replace(\".\", \"_\"),\n            )\n            qb.addSelect(mainAlias + \".\" + column.propertyPath, columnName)\n        })\n\n        // add conditions for the given entities\n        let condition1 = \"\"\n        if (columns.length === 1) {\n            const values = entities.map((entity) =>\n                columns[0].referencedColumn!.getEntityValue(entity),\n            )\n            const areAllNumbers = values.every(\n                (value) => typeof value === \"number\",\n            )\n\n            if (areAllNumbers) {\n                condition1 = `${mainAlias}.${\n                    columns[0].propertyPath\n                } IN (${values.join(\", \")})`\n            } else {\n                qb.setParameter(\"values1\", values)\n                condition1 =\n                    mainAlias +\n                    \".\" +\n                    columns[0].propertyPath +\n                    \" IN (:...values1)\" // todo: use ANY for postgres\n            }\n        } else {\n            condition1 =\n                \"(\" +\n                entities\n                    .map((entity, entityIndex) => {\n                        return columns\n                            .map((column) => {\n                                const paramName =\n                                    \"entity1_\" +\n                                    entityIndex +\n                                    \"_\" +\n                                    column.propertyName\n                                qb.setParameter(\n                                    paramName,\n                                    column.referencedColumn!.getEntityValue(\n                                        entity,\n                                    ),\n                                )\n                                return (\n                                    mainAlias +\n                                    \".\" +\n                                    column.propertyPath +\n                                    \" = :\" +\n                                    paramName\n                                )\n                            })\n                            .join(\" AND \")\n                    })\n                    .map((condition) => \"(\" + condition + \")\")\n                    .join(\" OR \") +\n                \")\"\n        }\n\n        // add conditions for the given inverse entities\n        let condition2 = \"\"\n        if (relatedEntities) {\n            if (inverseColumns.length === 1) {\n                const values = relatedEntities.map((entity) =>\n                    inverseColumns[0].referencedColumn!.getEntityValue(entity),\n                )\n                const areAllNumbers = values.every(\n                    (value) => typeof value === \"number\",\n                )\n\n                if (areAllNumbers) {\n                    condition2 = `${mainAlias}.${\n                        inverseColumns[0].propertyPath\n                    } IN (${values.join(\", \")})`\n                } else {\n                    qb.setParameter(\"values2\", values)\n                    condition2 =\n                        mainAlias +\n                        \".\" +\n                        inverseColumns[0].propertyPath +\n                        \" IN (:...values2)\" // todo: use ANY for postgres\n                }\n            } else {\n                condition2 =\n                    \"(\" +\n                    relatedEntities\n                        .map((entity, entityIndex) => {\n                            return inverseColumns\n                                .map((column) => {\n                                    const paramName =\n                                        \"entity2_\" +\n                                        entityIndex +\n                                        \"_\" +\n                                        column.propertyName\n                                    qb.setParameter(\n                                        paramName,\n                                        column.referencedColumn!.getEntityValue(\n                                            entity,\n                                        ),\n                                    )\n                                    return (\n                                        mainAlias +\n                                        \".\" +\n                                        column.propertyPath +\n                                        \" = :\" +\n                                        paramName\n                                    )\n                                })\n                                .join(\" AND \")\n                        })\n                        .map((condition) => \"(\" + condition + \")\")\n                        .join(\" OR \") +\n                    \")\"\n            }\n        }\n\n        // qb.from(junctionMetadata.target, mainAlias)\n        //     .where(condition1 + (condition2 ? \" AND \" + condition2 : \"\"));\n        //\n        // // execute query\n        // const { values1, values2 } = qb.getParameters();\n        // console.log(`I can do it`, { values1, values2 });\n        // if (inverseColumns.length === 1 &&\n        //     columns.length === 1 &&\n        //     this.connection.driver instanceof SqliteDriver &&\n        //     (values1.length + values2.length) > 500 &&\n        //     values1.length === values2.length) {\n        //     console.log(`I can do it`);\n        //     return qb.getRawMany();\n        //\n        // } else {\n        //     return qb.getRawMany();\n        // }\n\n        // execute query\n        const condition = [condition1, condition2]\n            .filter((v) => v.length > 0)\n            .join(\" AND \")\n        return qb\n            .from(junctionMetadata.target, mainAlias)\n            .where(condition)\n            .getRawMany()\n    }\n\n    /**\n     * Loads relation ids for the many-to-one and one-to-one owner relations.\n     */\n    protected loadForManyToOneAndOneToOneOwner(\n        relation: RelationMetadata,\n        entities: ObjectLiteral[],\n        relatedEntities?: ObjectLiteral[],\n    ) {\n        const mainAlias = relation.entityMetadata.targetName\n\n        // console.log(\"entitiesx\", entities);\n        // console.log(\"relatedEntitiesx\", relatedEntities);\n        const hasAllJoinColumnsInEntity = relation.joinColumns.every(\n            (joinColumn) => {\n                return !!relation.entityMetadata.nonVirtualColumns.find(\n                    (column) => column === joinColumn,\n                )\n            },\n        )\n        if (relatedEntities && hasAllJoinColumnsInEntity) {\n            const relationIdMaps: ObjectLiteral[] = []\n            entities.forEach((entity) => {\n                const relationIdMap: ObjectLiteral = {}\n                relation.entityMetadata.primaryColumns.forEach(\n                    (primaryColumn) => {\n                        const key =\n                            primaryColumn.entityMetadata.name +\n                            \"_\" +\n                            primaryColumn.propertyPath.replace(\".\", \"_\")\n                        relationIdMap[key] =\n                            primaryColumn.getEntityValue(entity)\n                    },\n                )\n\n                relatedEntities.forEach((relatedEntity) => {\n                    relation.joinColumns.forEach((joinColumn) => {\n                        const entityColumnValue =\n                            joinColumn.getEntityValue(entity)\n                        const relatedEntityColumnValue =\n                            joinColumn.referencedColumn!.getEntityValue(\n                                relatedEntity,\n                            )\n                        if (\n                            entityColumnValue === undefined ||\n                            relatedEntityColumnValue === undefined\n                        )\n                            return\n\n                        if (entityColumnValue === relatedEntityColumnValue) {\n                            const key =\n                                joinColumn.referencedColumn!.entityMetadata\n                                    .name +\n                                \"_\" +\n                                relation.propertyPath.replace(\".\", \"_\") +\n                                \"_\" +\n                                joinColumn.referencedColumn!.propertyPath.replace(\n                                    \".\",\n                                    \"_\",\n                                )\n                            relationIdMap[key] = relatedEntityColumnValue\n                        }\n                    })\n                })\n                if (\n                    Object.keys(relationIdMap).length ===\n                    relation.entityMetadata.primaryColumns.length +\n                        relation.joinColumns.length\n                ) {\n                    relationIdMaps.push(relationIdMap)\n                }\n            })\n            // console.log(\"relationIdMap\", relationIdMaps);\n            // console.log(\"entities.length\", entities.length);\n            if (relationIdMaps.length === entities.length)\n                return Promise.resolve(relationIdMaps)\n        }\n\n        // select all columns we need\n        const qb = this.connection.createQueryBuilder(this.queryRunner)\n        relation.entityMetadata.primaryColumns.forEach((primaryColumn) => {\n            const columnName = DriverUtils.buildAlias(\n                this.connection.driver,\n                undefined,\n                primaryColumn.entityMetadata.name +\n                    \"_\" +\n                    primaryColumn.propertyPath.replace(\".\", \"_\"),\n            )\n            qb.addSelect(\n                mainAlias + \".\" + primaryColumn.propertyPath,\n                columnName,\n            )\n        })\n        relation.joinColumns.forEach((column) => {\n            const columnName = DriverUtils.buildAlias(\n                this.connection.driver,\n                undefined,\n                column.referencedColumn!.entityMetadata.name +\n                    \"_\" +\n                    relation.propertyPath.replace(\".\", \"_\") +\n                    \"_\" +\n                    column.referencedColumn!.propertyPath.replace(\".\", \"_\"),\n            )\n            qb.addSelect(mainAlias + \".\" + column.propertyPath, columnName)\n        })\n\n        // add condition for entities\n        let condition: string = \"\"\n        if (relation.entityMetadata.primaryColumns.length === 1) {\n            const values = entities.map((entity) =>\n                relation.entityMetadata.primaryColumns[0].getEntityValue(\n                    entity,\n                ),\n            )\n            const areAllNumbers = values.every(\n                (value) => typeof value === \"number\",\n            )\n\n            if (areAllNumbers) {\n                condition = `${mainAlias}.${\n                    relation.entityMetadata.primaryColumns[0].propertyPath\n                } IN (${values.join(\", \")})`\n            } else {\n                qb.setParameter(\"values\", values)\n                condition =\n                    mainAlias +\n                    \".\" +\n                    relation.entityMetadata.primaryColumns[0].propertyPath +\n                    \" IN (:...values)\" // todo: use ANY for postgres\n            }\n        } else {\n            condition = entities\n                .map((entity, entityIndex) => {\n                    return relation.entityMetadata.primaryColumns\n                        .map((column, columnIndex) => {\n                            const paramName =\n                                \"entity\" + entityIndex + \"_\" + columnIndex\n                            qb.setParameter(\n                                paramName,\n                                column.getEntityValue(entity),\n                            )\n                            return (\n                                mainAlias +\n                                \".\" +\n                                column.propertyPath +\n                                \" = :\" +\n                                paramName\n                            )\n                        })\n                        .join(\" AND \")\n                })\n                .map((condition) => \"(\" + condition + \")\")\n                .join(\" OR \")\n        }\n\n        // execute query\n        return qb\n            .from(relation.entityMetadata.target, mainAlias)\n            .where(condition)\n            .getRawMany()\n    }\n\n    /**\n     * Loads relation ids for the one-to-many and one-to-one not owner relations.\n     */\n    protected loadForOneToManyAndOneToOneNotOwner(\n        relation: RelationMetadata,\n        entities: ObjectLiteral[],\n        relatedEntities?: ObjectLiteral[],\n    ) {\n        const originalRelation = relation\n        relation = relation.inverseRelation!\n\n        if (\n            relation.entityMetadata.primaryColumns.length ===\n            relation.joinColumns.length\n        ) {\n            const sameReferencedColumns =\n                relation.entityMetadata.primaryColumns.every((column) => {\n                    return relation.joinColumns.indexOf(column) !== -1\n                })\n            if (sameReferencedColumns) {\n                return Promise.resolve(\n                    entities.map((entity) => {\n                        const result: ObjectLiteral = {}\n                        relation.joinColumns.forEach(function (joinColumn) {\n                            const value =\n                                joinColumn.referencedColumn!.getEntityValue(\n                                    entity,\n                                )\n                            const joinColumnName =\n                                joinColumn.referencedColumn!.entityMetadata\n                                    .name +\n                                \"_\" +\n                                joinColumn.referencedColumn!.propertyPath.replace(\n                                    \".\",\n                                    \"_\",\n                                )\n                            const primaryColumnName =\n                                joinColumn.entityMetadata.name +\n                                \"_\" +\n                                originalRelation.propertyPath.replace(\n                                    \".\",\n                                    \"_\",\n                                ) +\n                                \"_\" +\n                                joinColumn.propertyPath.replace(\".\", \"_\")\n                            result[joinColumnName] = value\n                            result[primaryColumnName] = value\n                        })\n                        return result\n                    }),\n                )\n            }\n        }\n\n        const mainAlias = relation.entityMetadata.targetName\n\n        // select all columns we need\n        const qb = this.connection.createQueryBuilder(this.queryRunner)\n        relation.entityMetadata.primaryColumns.forEach((primaryColumn) => {\n            const columnName = DriverUtils.buildAlias(\n                this.connection.driver,\n                undefined,\n                primaryColumn.entityMetadata.name +\n                    \"_\" +\n                    originalRelation.propertyPath.replace(\".\", \"_\") +\n                    \"_\" +\n                    primaryColumn.propertyPath.replace(\".\", \"_\"),\n            )\n            qb.addSelect(\n                mainAlias + \".\" + primaryColumn.propertyPath,\n                columnName,\n            )\n        })\n        relation.joinColumns.forEach((column) => {\n            const columnName = DriverUtils.buildAlias(\n                this.connection.driver,\n                undefined,\n                column.referencedColumn!.entityMetadata.name +\n                    \"_\" +\n                    column.referencedColumn!.propertyPath.replace(\".\", \"_\"),\n            )\n            qb.addSelect(mainAlias + \".\" + column.propertyPath, columnName)\n        })\n\n        // add condition for entities\n        let condition: string = \"\"\n        if (relation.joinColumns.length === 1) {\n            const values = entities.map((entity) =>\n                relation.joinColumns[0].referencedColumn!.getEntityValue(\n                    entity,\n                ),\n            )\n            const areAllNumbers = values.every(\n                (value) => typeof value === \"number\",\n            )\n\n            if (areAllNumbers) {\n                condition = `${mainAlias}.${\n                    relation.joinColumns[0].propertyPath\n                } IN (${values.join(\", \")})`\n            } else {\n                qb.setParameter(\"values\", values)\n                condition =\n                    mainAlias +\n                    \".\" +\n                    relation.joinColumns[0].propertyPath +\n                    \" IN (:...values)\" // todo: use ANY for postgres\n            }\n        } else {\n            condition = entities\n                .map((entity, entityIndex) => {\n                    return relation.joinColumns\n                        .map((joinColumn, joinColumnIndex) => {\n                            const paramName =\n                                \"entity\" + entityIndex + \"_\" + joinColumnIndex\n                            qb.setParameter(\n                                paramName,\n                                joinColumn.referencedColumn!.getEntityValue(\n                                    entity,\n                                ),\n                            )\n                            return (\n                                mainAlias +\n                                \".\" +\n                                joinColumn.propertyPath +\n                                \" = :\" +\n                                paramName\n                            )\n                        })\n                        .join(\" AND \")\n                })\n                .map((condition) => \"(\" + condition + \")\")\n                .join(\" OR \")\n        }\n\n        // execute query\n        return qb\n            .from(relation.entityMetadata.target, mainAlias)\n            .where(condition)\n            .getRawMany()\n    }\n}\n"], "sourceRoot": ".."}