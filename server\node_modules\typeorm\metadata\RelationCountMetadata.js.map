{"version": 3, "sources": ["../../src/metadata/RelationCountMetadata.ts"], "names": [], "mappings": ";;;AAIA,oCAAuC;AAEvC;;GAEG;AACH,MAAa,qBAAqB;IA0C9B,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAGX;QACG,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;QACjC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,CAAA;QAC7C,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAA;QAClD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAA;QAC/B,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAA;IAC/D,CAAC;IAED,wEAAwE;IACxE,yBAAyB;IACzB,wEAAwE;IAExE;;;OAGG;IACH,KAAK;QACD,MAAM,YAAY,GACd,OAAO,IAAI,CAAC,qBAAqB,KAAK,UAAU;YAC5C,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;YAC/D,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAA;QACpC,MAAM,QAAQ,GACV,IAAI,CAAC,cAAc,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAA;QAClE,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,oBAAY,CAClB,wBAAwB,YAAY,0DAA0D,CACjG,CAAA;QAEL,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC5B,CAAC;CACJ;AAhFD,sDAgFC", "file": "RelationCountMetadata.js", "sourcesContent": ["import { RelationCountMetadataArgs } from \"../metadata-args/RelationCountMetadataArgs\"\nimport { EntityMetadata } from \"./EntityMetadata\"\nimport { RelationMetadata } from \"./RelationMetadata\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport { TypeORMError } from \"../error\"\n\n/**\n * Contains all information about entity's relation count.\n */\nexport class RelationCountMetadata {\n    // ---------------------------------------------------------------------\n    // Public Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Entity metadata where this column metadata is.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Relation which needs to be counted.\n     */\n    relation: RelationMetadata\n\n    /**\n     * Relation name which need to count.\n     */\n    relationNameOrFactory: string | ((object: any) => any)\n\n    /**\n     * Target class to which metadata is applied.\n     */\n    target: Function | string\n\n    /**\n     * Target's property name to which this metadata is applied.\n     */\n    propertyName: string\n\n    /**\n     * Alias of the joined (destination) table.\n     */\n    alias?: string\n\n    /**\n     * Extra condition applied to \"ON\" section of join.\n     */\n    queryBuilderFactory?: (\n        qb: SelectQueryBuilder<any>,\n    ) => SelectQueryBuilder<any>\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        args: RelationCountMetadataArgs\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.target = options.args.target\n        this.propertyName = options.args.propertyName\n        this.relationNameOrFactory = options.args.relation\n        this.alias = options.args.alias\n        this.queryBuilderFactory = options.args.queryBuilderFactory\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Builder Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds some depend relation count metadata properties.\n     * This builder method should be used only after entity metadata, its properties map and all relations are build.\n     */\n    build() {\n        const propertyPath =\n            typeof this.relationNameOrFactory === \"function\"\n                ? this.relationNameOrFactory(this.entityMetadata.propertiesMap)\n                : this.relationNameOrFactory\n        const relation =\n            this.entityMetadata.findRelationWithPropertyPath(propertyPath)\n        if (!relation)\n            throw new TypeORMError(\n                `Cannot find relation ${propertyPath}. Wrong relation specified for @RelationCount decorator.`,\n            )\n\n        this.relation = relation\n    }\n}\n"], "sourceRoot": ".."}