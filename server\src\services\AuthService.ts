import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { Repository } from 'typeorm'
import { AppDataSource } from '../config/database'
import redis from '../config/redis'
import { config } from '../config'
import { User } from '../entities/User'
import { Role } from '../entities/Role'
import { Menu } from '../entities/Menu'
import { logger } from '../utils/logger'

export class AuthService {
  private userRepository: Repository<User>
  private roleRepository: Repository<Role>
  private menuRepository: Repository<Menu>

  constructor() {
    this.userRepository = AppDataSource.getRepository(User)
    this.roleRepository = AppDataSource.getRepository(Role)
    this.menuRepository = AppDataSource.getRepository(Menu)
  }

  // 用户登录
  async login(username: string, password: string) {
    // 查找用户
    const user = await this.userRepository.findOne({
      where: { username, status: 1 },
      relations: ['department', 'roles']
    })

    if (!user) {
      throw new Error('用户不存在或已被禁用')
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      throw new Error('密码错误')
    }

    // 获取用户权限
    const permissions = await this.getUserPermissions(user.id)
    const roles = user.roles.map(role => role.code)

    // 生成JWT Token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        deptId: user.deptId
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    )

    // 缓存用户会话信息
    const sessionData = {
      id: user.id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      deptId: user.deptId,
      deptName: user.department?.name,
      roles,
      permissions,
      loginTime: new Date().toISOString()
    }

    await redis.setex(`user_session:${user.id}`, 86400, JSON.stringify(sessionData))

    logger.info(`User ${username} logged in successfully`)

    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        deptId: user.deptId,
        deptName: user.department?.name,
        status: user.status
      },
      permissions,
      roles
    }
  }

  // 获取用户权限
  async getUserPermissions(userId: number): Promise<string[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles', 'roles.menus']
    })

    if (!user) {
      return []
    }

    const permissions = new Set<string>()

    user.roles.forEach(role => {
      role.menus.forEach(menu => {
        if (menu.permission && menu.status === 1) {
          permissions.add(menu.permission)
        }
      })
    })

    return Array.from(permissions)
  }

  // 获取用户菜单
  async getUserMenus(userId: number) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles', 'roles.menus']
    })

    if (!user) {
      return []
    }

    const menuIds = new Set<number>()
    user.roles.forEach(role => {
      role.menus.forEach(menu => {
        if (menu.status === 1) {
          menuIds.add(menu.id)
        }
      })
    })

    // 获取所有相关菜单（包括父级菜单）
    const menus = await this.menuRepository.find({
      where: { status: 1 },
      order: { sortOrder: 'ASC' }
    })

    // 构建菜单树
    return this.buildMenuTree(menus.filter(menu => menuIds.has(menu.id)))
  }

  // 构建菜单树
  private buildMenuTree(menus: Menu[], parentId = 0): Menu[] {
    return menus
      .filter(menu => menu.parentId === parentId)
      .map(menu => ({
        ...menu,
        children: this.buildMenuTree(menus, menu.id)
      }))
  }

  // 验证Token
  async validateToken(token: string) {
    try {
      const decoded = jwt.verify(token, config.jwt.secret) as any

      // 从Redis获取用户会话
      const sessionData = await redis.get(`user_session:${decoded.userId}`)
      if (!sessionData) {
        throw new Error('会话已过期')
      }

      return JSON.parse(sessionData)
    } catch (error) {
      throw new Error('Token无效')
    }
  }

  // 用户登出
  async logout(userId: number) {
    await redis.del(`user_session:${userId}`)
    logger.info(`User ${userId} logged out`)
  }

  // 修改密码
  async changePassword(userId: number, oldPassword: string, newPassword: string) {
    const user = await this.userRepository.findOne({ where: { id: userId } })
    if (!user) {
      throw new Error('用户不存在')
    }

    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password)
    if (!isOldPasswordValid) {
      throw new Error('原密码错误')
    }

    const hashedPassword = await bcrypt.hash(newPassword, config.bcrypt.rounds)
    await this.userRepository.update(userId, { password: hashedPassword })

    logger.info(`User ${userId} changed password`)
  }
}
