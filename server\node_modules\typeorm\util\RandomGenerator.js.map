{"version": 3, "sources": ["../../src/util/RandomGenerator.ts"], "names": [], "mappings": ";;;AAAA,MAAa,eAAe;IACxB;;;;;;;;;;;OAWG;IACH,MAAM,CAAC,IAAI,CAAC,GAAW;QACnB,MAAM,QAAQ,GAAG,UAAU,CAAM,EAAE,CAAM;YACrC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;YACtC,OAAO,EAAE,CAAA;QACb,CAAC,CAAA;QAED,MAAM,OAAO,GAAG,UAAU,GAAQ;YAC9B,IAAI,GAAG,GAAG,EAAE,CAAA;YACZ,IAAI,CAAC,CAAA;YACL,IAAI,CAAC,CAAA;YAEL,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;gBAC5B,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;YACzB,CAAC;YACD,OAAO,GAAG,CAAA;QACd,CAAC,CAAA;QAED,IAAI,UAAU,CAAA;QACd,IAAI,CAAC,EAAE,CAAC,CAAA;QACR,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,CAAA;QACvB,IAAI,EAAE,GAAG,UAAU,CAAA;QACnB,IAAI,EAAE,GAAG,UAAU,CAAA;QACnB,IAAI,EAAE,GAAG,UAAU,CAAA;QACnB,IAAI,EAAE,GAAG,UAAU,CAAA;QACnB,IAAI,EAAE,GAAG,UAAU,CAAA;QACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QACjB,IAAI,IAAI,CAAA;QAER,cAAc;QACd,GAAG,GAAG,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA;QAC1C,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;QAEzB,MAAM,SAAS,GAAG,EAAE,CAAA;QACpB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,CAAC;gBACG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBACzB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC7B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;oBAC5B,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YACzB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrB,CAAC;QAED,QAAQ,MAAM,GAAG,CAAC,EAAE,CAAC;YACjB,KAAK,CAAC;gBACF,CAAC,GAAG,WAAW,CAAA;gBACf,MAAK;YACT,KAAK,CAAC;gBACF,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,SAAS,CAAA;gBAClD,MAAK;YACT,KAAK,CAAC;gBACF,CAAC;oBACG,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;wBAClC,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;wBAClC,OAAO,CAAA;gBACX,MAAK;YACT,KAAK,CAAC;gBACF,CAAC;oBACG,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;wBAClC,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;wBAClC,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;wBACjC,IAAI,CAAA;gBACR,MAAK;QACb,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAEjB,OAAO,SAAS,CAAC,MAAM,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC;YAClC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrB,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,MAAM,KAAK,EAAE,CAAC,CAAA;QAC7B,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;QAE3C,KAAK,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,SAAS,CAAC,MAAM,EAAE,UAAU,IAAI,EAAE,EAAE,CAAC;YACnE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;YACpC,CAAC;YACD,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;YACnE,CAAC;YAED,CAAC,GAAG,EAAE,CAAA;YACN,CAAC,GAAG,EAAE,CAAA;YACN,CAAC,GAAG,EAAE,CAAA;YACN,CAAC,GAAG,EAAE,CAAA;YACN,CAAC,GAAG,EAAE,CAAA;YAEN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvB,IAAI;oBACA,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;wBACpB,CAAC;wBACD,CAAC,CAAC,CAAC,CAAC;wBACJ,UAAU,CAAC;wBACf,WAAW,CAAA;gBACf,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;gBACnB,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,IAAI,CAAA;YACZ,CAAC;YAED,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxB,IAAI;oBACA,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;wBACtD,WAAW,CAAA;gBACf,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;gBACnB,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,IAAI,CAAA;YACZ,CAAC;YAED,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxB,IAAI;oBACA,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;wBAC7B,CAAC;wBACD,CAAC,CAAC,CAAC,CAAC;wBACJ,UAAU,CAAC;wBACf,WAAW,CAAA;gBACf,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;gBACnB,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,IAAI,CAAA;YACZ,CAAC;YAED,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxB,IAAI;oBACA,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;wBACtD,WAAW,CAAA;gBACf,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;gBACnB,CAAC,GAAG,CAAC,CAAA;gBACL,CAAC,GAAG,IAAI,CAAA;YACZ,CAAC;YAED,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,WAAW,CAAA;YAC3B,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,WAAW,CAAA;YAC3B,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,WAAW,CAAA;YAC3B,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,WAAW,CAAA;YAC3B,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,WAAW,CAAA;QAC/B,CAAC;QAED,IAAI;YACA,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAA;QACvE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAA;IAC7B,CAAC;CACJ;AApKD,0CAoKC", "file": "RandomGenerator.js", "sourcesContent": ["export class RandomGenerator {\n    /**\n     *  discuss at: http://locutus.io/php/sha1/\n     * original by: Webtoolkit.info (http://www.webtoolkit.info/)\n     * improved by: <PERSON> (http://getsprink.com)\n     * improved by: <PERSON> (http://kvz.io)\n     *    input by: <PERSON> (http://brett-zamir.me)\n     *      note 1: Keep in mind that in accordance with PHP, the whole string is buffered and then\n     *      note 1: hashed. If available, we'd recommend using Node's native crypto modules directly\n     *      note 1: in a steaming fashion for faster and more efficient hashing\n     *   example 1: sha1('<PERSON>')\n     *   returns 1: '54916d2e62f65b3afa6e192e6a601cdbe5cb5897'\n     */\n    static sha1(str: string) {\n        const _rotLeft = function (n: any, s: any) {\n            const t4 = (n << s) | (n >>> (32 - s))\n            return t4\n        }\n\n        const _cvtHex = function (val: any) {\n            let str = \"\"\n            let i\n            let v\n\n            for (i = 7; i >= 0; i--) {\n                v = (val >>> (i * 4)) & 0x0f\n                str += v.toString(16)\n            }\n            return str\n        }\n\n        let blockstart\n        let i, j\n        const W = new Array(80)\n        let H0 = 0x67452301\n        let H1 = 0xefcdab89\n        let H2 = 0x98badcfe\n        let H3 = 0x10325476\n        let H4 = 0xc3d2e1f0\n        let A, B, C, D, E\n        let temp\n\n        // utf8_encode\n        str = /*unescape*/ encodeURIComponent(str)\n        const strLen = str.length\n\n        const wordArray = []\n        for (i = 0; i < strLen - 3; i += 4) {\n            j =\n                (str.charCodeAt(i) << 24) |\n                (str.charCodeAt(i + 1) << 16) |\n                (str.charCodeAt(i + 2) << 8) |\n                str.charCodeAt(i + 3)\n            wordArray.push(j)\n        }\n\n        switch (strLen % 4) {\n            case 0:\n                i = 0x080000000\n                break\n            case 1:\n                i = (str.charCodeAt(strLen - 1) << 24) | 0x0800000\n                break\n            case 2:\n                i =\n                    (str.charCodeAt(strLen - 2) << 24) |\n                    (str.charCodeAt(strLen - 1) << 16) |\n                    0x08000\n                break\n            case 3:\n                i =\n                    (str.charCodeAt(strLen - 3) << 24) |\n                    (str.charCodeAt(strLen - 2) << 16) |\n                    (str.charCodeAt(strLen - 1) << 8) |\n                    0x80\n                break\n        }\n\n        wordArray.push(i)\n\n        while (wordArray.length % 16 !== 14) {\n            wordArray.push(0)\n        }\n\n        wordArray.push(strLen >>> 29)\n        wordArray.push((strLen << 3) & 0x0ffffffff)\n\n        for (blockstart = 0; blockstart < wordArray.length; blockstart += 16) {\n            for (i = 0; i < 16; i++) {\n                W[i] = wordArray[blockstart + i]\n            }\n            for (i = 16; i <= 79; i++) {\n                W[i] = _rotLeft(W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16], 1)\n            }\n\n            A = H0\n            B = H1\n            C = H2\n            D = H3\n            E = H4\n\n            for (i = 0; i <= 19; i++) {\n                temp =\n                    (_rotLeft(A, 5) +\n                        ((B & C) | (~B & D)) +\n                        E +\n                        W[i] +\n                        0x5a827999) &\n                    0x0ffffffff\n                E = D\n                D = C\n                C = _rotLeft(B, 30)\n                B = A\n                A = temp\n            }\n\n            for (i = 20; i <= 39; i++) {\n                temp =\n                    (_rotLeft(A, 5) + (B ^ C ^ D) + E + W[i] + 0x6ed9eba1) &\n                    0x0ffffffff\n                E = D\n                D = C\n                C = _rotLeft(B, 30)\n                B = A\n                A = temp\n            }\n\n            for (i = 40; i <= 59; i++) {\n                temp =\n                    (_rotLeft(A, 5) +\n                        ((B & C) | (B & D) | (C & D)) +\n                        E +\n                        W[i] +\n                        0x8f1bbcdc) &\n                    0x0ffffffff\n                E = D\n                D = C\n                C = _rotLeft(B, 30)\n                B = A\n                A = temp\n            }\n\n            for (i = 60; i <= 79; i++) {\n                temp =\n                    (_rotLeft(A, 5) + (B ^ C ^ D) + E + W[i] + 0xca62c1d6) &\n                    0x0ffffffff\n                E = D\n                D = C\n                C = _rotLeft(B, 30)\n                B = A\n                A = temp\n            }\n\n            H0 = (H0 + A) & 0x0ffffffff\n            H1 = (H1 + B) & 0x0ffffffff\n            H2 = (H2 + C) & 0x0ffffffff\n            H3 = (H3 + D) & 0x0ffffffff\n            H4 = (H4 + E) & 0x0ffffffff\n        }\n\n        temp =\n            _cvtHex(H0) + _cvtHex(H1) + _cvtHex(H2) + _cvtHex(H3) + _cvtHex(H4)\n        return temp.toLowerCase()\n    }\n}\n"], "sourceRoot": ".."}