import { Repository, Like, In } from 'typeorm'
import { AppDataSource } from '../config/database'
import { Role } from '../entities/Role'
import { Menu } from '../entities/Menu'
import { logger } from '../utils/logger'

export interface RoleListParams {
  page?: number
  pageSize?: number
  name?: string
  code?: string
  status?: number
}

export interface CreateRoleParams {
  name: string
  code: string
  description?: string
  dataScope?: number
  menuIds?: number[]
}

export interface UpdateRoleParams {
  id: number
  name?: string
  code?: string
  description?: string
  dataScope?: number
  status?: number
  menuIds?: number[]
}

export class RoleService {
  private roleRepository: Repository<Role>
  private menuRepository: Repository<Menu>

  constructor() {
    this.roleRepository = AppDataSource.getRepository(Role)
    this.menuRepository = AppDataSource.getRepository(Menu)
  }

  // 获取角色列表
  async getRoleList(params: RoleListParams) {
    const {
      page = 1,
      pageSize = 10,
      name,
      code,
      status
    } = params

    const queryBuilder = this.roleRepository
      .createQueryBuilder('role')
      .leftJoinAndSelect('role.menus', 'menus')

    // 添加查询条件
    if (name) {
      queryBuilder.andWhere('role.name LIKE :name', { name: `%${name}%` })
    }
    if (code) {
      queryBuilder.andWhere('role.code LIKE :code', { code: `%${code}%` })
    }
    if (status !== undefined) {
      queryBuilder.andWhere('role.status = :status', { status })
    }

    // 分页
    const skip = (page - 1) * pageSize
    queryBuilder.skip(skip).take(pageSize)

    // 排序
    queryBuilder.orderBy('role.createdAt', 'DESC')

    const [list, total] = await queryBuilder.getManyAndCount()

    // 处理返回数据
    const roles = list.map(role => ({
      ...role,
      menuIds: role.menus?.map(menu => menu.id) || [],
      menuNames: role.menus?.map(menu => menu.name) || []
    }))

    return {
      list: roles,
      total,
      page,
      pageSize
    }
  }

  // 根据ID获取角色详情
  async getRoleById(id: number) {
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: ['menus']
    })

    if (!role) {
      throw new Error('角色不存在')
    }

    return {
      ...role,
      menuIds: role.menus?.map(menu => menu.id) || [],
      menuNames: role.menus?.map(menu => menu.name) || []
    }
  }

  // 创建角色
  async createRole(params: CreateRoleParams) {
    const { name, code, description, dataScope = 4, menuIds } = params

    // 检查角色名是否已存在
    const existingName = await this.roleRepository.findOne({ where: { name } })
    if (existingName) {
      throw new Error('角色名已存在')
    }

    // 检查角色编码是否已存在
    const existingCode = await this.roleRepository.findOne({ where: { code } })
    if (existingCode) {
      throw new Error('角色编码已存在')
    }

    // 创建角色
    const role = this.roleRepository.create({
      name,
      code,
      description,
      dataScope,
      status: 1
    })

    // 分配菜单权限
    if (menuIds && menuIds.length > 0) {
      const menus = await this.menuRepository.findBy({ id: In(menuIds) })
      role.menus = menus
    }

    const savedRole = await this.roleRepository.save(role)

    logger.info(`Role created: ${name} (ID: ${savedRole.id})`)

    return this.getRoleById(savedRole.id)
  }

  // 更新角色
  async updateRole(params: UpdateRoleParams) {
    const { id, name, code, description, dataScope, status, menuIds } = params

    const role = await this.roleRepository.findOne({
      where: { id },
      relations: ['menus']
    })

    if (!role) {
      throw new Error('角色不存在')
    }

    // 检查角色名是否已被其他角色使用
    if (name && name !== role.name) {
      const existingName = await this.roleRepository.findOne({ where: { name } })
      if (existingName) {
        throw new Error('角色名已存在')
      }
      role.name = name
    }

    // 检查角色编码是否已被其他角色使用
    if (code && code !== role.code) {
      const existingCode = await this.roleRepository.findOne({ where: { code } })
      if (existingCode) {
        throw new Error('角色编码已存在')
      }
      role.code = code
    }

    // 更新其他字段
    if (description !== undefined) role.description = description
    if (dataScope !== undefined) role.dataScope = dataScope
    if (status !== undefined) role.status = status

    // 更新菜单权限
    if (menuIds !== undefined) {
      if (menuIds.length > 0) {
        const menus = await this.menuRepository.findBy({ id: In(menuIds) })
        role.menus = menus
      } else {
        role.menus = []
      }
    }

    await this.roleRepository.save(role)

    logger.info(`Role updated: ${role.name} (ID: ${id})`)

    return this.getRoleById(id)
  }

  // 删除角色
  async deleteRole(id: number) {
    const role = await this.roleRepository.findOne({ 
      where: { id },
      relations: ['users']
    })
    
    if (!role) {
      throw new Error('角色不存在')
    }

    // 检查是否有用户使用该角色
    if (role.users && role.users.length > 0) {
      throw new Error('该角色下还有用户，无法删除')
    }

    await this.roleRepository.remove(role)

    logger.info(`Role deleted: ${role.name} (ID: ${id})`)
  }

  // 获取所有角色（用于下拉选择）
  async getAllRoles() {
    const roles = await this.roleRepository.find({
      where: { status: 1 },
      select: ['id', 'name', 'code'],
      order: { createdAt: 'ASC' }
    })

    return roles
  }

  // 批量删除角色
  async batchDeleteRoles(ids: number[]) {
    const roles = await this.roleRepository.find({
      where: { id: In(ids) },
      relations: ['users']
    })

    if (roles.length === 0) {
      throw new Error('未找到要删除的角色')
    }

    // 检查是否有角色下还有用户
    const rolesWithUsers = roles.filter(role => role.users && role.users.length > 0)
    if (rolesWithUsers.length > 0) {
      const roleNames = rolesWithUsers.map(role => role.name).join('、')
      throw new Error(`角色 ${roleNames} 下还有用户，无法删除`)
    }

    await this.roleRepository.remove(roles)

    logger.info(`Batch deleted ${roles.length} roles`)
  }

  // 分配菜单权限
  async assignMenus(roleId: number, menuIds: number[]) {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['menus']
    })

    if (!role) {
      throw new Error('角色不存在')
    }

    const menus = await this.menuRepository.findBy({ id: In(menuIds) })
    role.menus = menus

    await this.roleRepository.save(role)

    logger.info(`Assigned ${menuIds.length} menus to role: ${role.name}`)

    return this.getRoleById(roleId)
  }
}
