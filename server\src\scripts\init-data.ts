import 'reflect-metadata'
import bcrypt from 'bcryptjs'
import { AppDataSource } from '../config/database'
import { User } from '../entities/User'
import { Role } from '../entities/Role'
import { Menu, MenuType } from '../entities/Menu'
import { Department } from '../entities/Department'
import { logger } from '../utils/logger'

async function initializeData() {
  try {
    // 初始化数据库连接
    await AppDataSource.initialize()
    logger.info('Database connected for data initialization')

    const userRepository = AppDataSource.getRepository(User)
    const roleRepository = AppDataSource.getRepository(Role)
    const menuRepository = AppDataSource.getRepository(Menu)
    const deptRepository = AppDataSource.getRepository(Department)

    // 1. 创建部门
    const rootDept = await deptRepository.save({
      name: '总公司',
      parentId: 0,
      sortOrder: 1,
      status: 1
    })

    const techDept = await deptRepository.save({
      name: '技术部',
      parentId: rootDept.id,
      sortOrder: 1,
      status: 1
    })

    const adminDept = await deptRepository.save({
      name: '行政部',
      parentId: rootDept.id,
      sortOrder: 2,
      status: 1
    })

    // 2. 创建菜单
    const dashboardMenu = await menuRepository.save({
      name: '仪表盘',
      parentId: 0,
      path: '/dashboard',
      component: 'dashboard/index',
      permission: 'dashboard:view',
      type: MenuType.MENU,
      icon: 'Dashboard',
      sortOrder: 1,
      status: 1
    })

    const systemMenu = await menuRepository.save({
      name: '系统管理',
      parentId: 0,
      path: '/system',
      component: 'layout/index',
      permission: 'system:view',
      type: MenuType.DIRECTORY,
      icon: 'Setting',
      sortOrder: 2,
      status: 1
    })

    const userMenu = await menuRepository.save({
      name: '用户管理',
      parentId: systemMenu.id,
      path: '/system/user',
      component: 'system/user/index',
      permission: 'system:user:view',
      type: MenuType.MENU,
      icon: 'User',
      sortOrder: 1,
      status: 1
    })

    const userAddBtn = await menuRepository.save({
      name: '新增用户',
      parentId: userMenu.id,
      permission: 'system:user:add',
      type: MenuType.BUTTON,
      sortOrder: 1,
      status: 1
    })

    const userEditBtn = await menuRepository.save({
      name: '编辑用户',
      parentId: userMenu.id,
      permission: 'system:user:edit',
      type: MenuType.BUTTON,
      sortOrder: 2,
      status: 1
    })

    const userDeleteBtn = await menuRepository.save({
      name: '删除用户',
      parentId: userMenu.id,
      permission: 'system:user:delete',
      type: MenuType.BUTTON,
      sortOrder: 3,
      status: 1
    })

    const roleMenu = await menuRepository.save({
      name: '角色管理',
      parentId: systemMenu.id,
      path: '/system/role',
      component: 'system/role/index',
      permission: 'system:role:view',
      type: MenuType.MENU,
      icon: 'UserFilled',
      sortOrder: 2,
      status: 1
    })

    const menuMenu = await menuRepository.save({
      name: '菜单管理',
      parentId: systemMenu.id,
      path: '/system/menu',
      component: 'system/menu/index',
      permission: 'system:menu:view',
      type: MenuType.MENU,
      icon: 'Menu',
      sortOrder: 3,
      status: 1
    })

    const deptMenu = await menuRepository.save({
      name: '部门管理',
      parentId: systemMenu.id,
      path: '/system/dept',
      component: 'system/dept/index',
      permission: 'system:dept:view',
      type: MenuType.MENU,
      icon: 'OfficeBuilding',
      sortOrder: 4,
      status: 1
    })

    // 3. 创建角色
    const adminRole = await roleRepository.save({
      name: '超级管理员',
      code: 'admin',
      description: '系统超级管理员，拥有所有权限',
      dataScope: 1, // 全部数据权限
      status: 1
    })

    const userRole = await roleRepository.save({
      name: '普通用户',
      code: 'user',
      description: '普通用户，基础权限',
      dataScope: 4, // 仅本人数据权限
      status: 1
    })

    // 4. 分配角色菜单权限
    const allMenus = [
      dashboardMenu, systemMenu, userMenu, userAddBtn, userEditBtn, userDeleteBtn,
      roleMenu, menuMenu, deptMenu
    ]
    
    adminRole.menus = allMenus
    await roleRepository.save(adminRole)

    userRole.menus = [dashboardMenu]
    await roleRepository.save(userRole)

    // 5. 创建用户
    const hashedPassword = await bcrypt.hash('123456', 10)

    const adminUser = await userRepository.save({
      username: 'admin',
      password: hashedPassword,
      email: '<EMAIL>',
      phone: '13800138000',
      status: 1,
      deptId: techDept.id
    })

    const testUser = await userRepository.save({
      username: 'test',
      password: hashedPassword,
      email: '<EMAIL>',
      phone: '13800138001',
      status: 1,
      deptId: adminDept.id
    })

    // 6. 分配用户角色
    adminUser.roles = [adminRole]
    await userRepository.save(adminUser)

    testUser.roles = [userRole]
    await userRepository.save(testUser)

    logger.info('Database initialization completed successfully')
    logger.info('Default users created:')
    logger.info('  - admin/123456 (超级管理员)')
    logger.info('  - test/123456 (普通用户)')

  } catch (error) {
    logger.error('Database initialization failed:', error)
    throw error
  } finally {
    await AppDataSource.destroy()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initializeData().catch(console.error)
}

export { initializeData }
