{"version": 3, "sources": ["../../src/driver/SqlInMemory.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACH,MAAa,WAAW;IAAxB;QACI,cAAS,GAAY,EAAE,CAAA;QACvB,gBAAW,GAAY,EAAE,CAAA;IAC7B,CAAC;CAAA;AAHD,kCAGC", "file": "SqlInMemory.js", "sourcesContent": ["import { Query } from \"./Query\"\n\n/**\n * This class stores up and down queries needed for migrations functionality.\n */\nexport class SqlInMemory {\n    upQueries: Query[] = []\n    downQueries: Query[] = []\n}\n"], "sourceRoot": ".."}