# 服务器配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 数据库配置
DB_HOST=************
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=nsl00123
DB_DATABASE=nsl

# Redis配置
REDIS_HOST=************
REDIS_PORT=6379
REDIS_PASSWORD=nsl00123
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# 加密配置
BCRYPT_ROUNDS=10

# 日志配置
LOG_LEVEL=debug
LOG_DIR=logs

# CORS配置
CORS_ORIGIN=http://localhost:5173

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
