import bcrypt from 'bcryptjs'
import { Repository, Like, In } from 'typeorm'
import { AppDataSource } from '../config/database'
import { config } from '../config'
import { User } from '../entities/User'
import { Role } from '../entities/Role'
import { Department } from '../entities/Department'
import { logger } from '../utils/logger'

export interface UserListParams {
  page?: number
  pageSize?: number
  username?: string
  email?: string
  phone?: string
  status?: number
  deptId?: number
}

export interface CreateUserParams {
  username: string
  password: string
  email?: string
  phone?: string
  deptId?: number
  roleIds?: number[]
}

export interface UpdateUserParams {
  id: number
  username?: string
  email?: string
  phone?: string
  status?: number
  deptId?: number
  roleIds?: number[]
}

export class UserService {
  private userRepository: Repository<User>
  private roleRepository: Repository<Role>
  private deptRepository: Repository<Department>

  constructor() {
    this.userRepository = AppDataSource.getRepository(User)
    this.roleRepository = AppDataSource.getRepository(Role)
    this.deptRepository = AppDataSource.getRepository(Department)
  }

  // 获取用户列表
  async getUserList(params: UserListParams) {
    const {
      page = 1,
      pageSize = 10,
      username,
      email,
      phone,
      status,
      deptId
    } = params

    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.department', 'dept')
      .leftJoinAndSelect('user.roles', 'roles')

    // 添加查询条件
    if (username) {
      queryBuilder.andWhere('user.username LIKE :username', { username: `%${username}%` })
    }
    if (email) {
      queryBuilder.andWhere('user.email LIKE :email', { email: `%${email}%` })
    }
    if (phone) {
      queryBuilder.andWhere('user.phone LIKE :phone', { phone: `%${phone}%` })
    }
    if (status !== undefined) {
      queryBuilder.andWhere('user.status = :status', { status })
    }
    if (deptId) {
      queryBuilder.andWhere('user.deptId = :deptId', { deptId })
    }

    // 分页
    const skip = (page - 1) * pageSize
    queryBuilder.skip(skip).take(pageSize)

    // 排序
    queryBuilder.orderBy('user.createdAt', 'DESC')

    const [list, total] = await queryBuilder.getManyAndCount()

    // 处理返回数据，移除密码字段
    const users = list.map(user => {
      const { password, ...userWithoutPassword } = user
      return {
        ...userWithoutPassword,
        deptName: user.department?.name,
        roleNames: user.roles?.map(role => role.name) || []
      }
    })

    return {
      list: users,
      total,
      page,
      pageSize
    }
  }

  // 根据ID获取用户详情
  async getUserById(id: number) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['department', 'roles']
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const { password, ...userWithoutPassword } = user
    return {
      ...userWithoutPassword,
      deptName: user.department?.name,
      roleIds: user.roles?.map(role => role.id) || [],
      roleNames: user.roles?.map(role => role.name) || []
    }
  }

  // 创建用户
  async createUser(params: CreateUserParams) {
    const { username, password, email, phone, deptId, roleIds } = params

    // 检查用户名是否已存在
    const existingUser = await this.userRepository.findOne({ where: { username } })
    if (existingUser) {
      throw new Error('用户名已存在')
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await this.userRepository.findOne({ where: { email } })
      if (existingEmail) {
        throw new Error('邮箱已存在')
      }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, config.bcrypt.rounds)

    // 创建用户
    const user = this.userRepository.create({
      username,
      password: hashedPassword,
      email,
      phone,
      deptId,
      status: 1
    })

    // 分配角色
    if (roleIds && roleIds.length > 0) {
      const roles = await this.roleRepository.findBy({ id: In(roleIds) })
      user.roles = roles
    }

    const savedUser = await this.userRepository.save(user)

    logger.info(`User created: ${username} (ID: ${savedUser.id})`)

    return this.getUserById(savedUser.id)
  }

  // 更新用户
  async updateUser(params: UpdateUserParams) {
    const { id, username, email, phone, status, deptId, roleIds } = params

    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['roles']
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    // 检查用户名是否已被其他用户使用
    if (username && username !== user.username) {
      const existingUser = await this.userRepository.findOne({ where: { username } })
      if (existingUser) {
        throw new Error('用户名已存在')
      }
      user.username = username
    }

    // 检查邮箱是否已被其他用户使用
    if (email && email !== user.email) {
      const existingEmail = await this.userRepository.findOne({ where: { email } })
      if (existingEmail) {
        throw new Error('邮箱已存在')
      }
      user.email = email
    }

    // 更新其他字段
    if (phone !== undefined) user.phone = phone
    if (status !== undefined) user.status = status
    if (deptId !== undefined) user.deptId = deptId

    // 更新角色
    if (roleIds !== undefined) {
      if (roleIds.length > 0) {
        const roles = await this.roleRepository.findBy({ id: In(roleIds) })
        user.roles = roles
      } else {
        user.roles = []
      }
    }

    await this.userRepository.save(user)

    logger.info(`User updated: ${user.username} (ID: ${id})`)

    return this.getUserById(id)
  }

  // 删除用户
  async deleteUser(id: number) {
    const user = await this.userRepository.findOne({ where: { id } })
    if (!user) {
      throw new Error('用户不存在')
    }

    await this.userRepository.remove(user)

    logger.info(`User deleted: ${user.username} (ID: ${id})`)
  }

  // 重置密码
  async resetPassword(id: number) {
    const user = await this.userRepository.findOne({ where: { id } })
    if (!user) {
      throw new Error('用户不存在')
    }

    const newPassword = '123456' // 默认密码
    const hashedPassword = await bcrypt.hash(newPassword, config.bcrypt.rounds)

    await this.userRepository.update(id, { password: hashedPassword })

    logger.info(`Password reset for user: ${user.username} (ID: ${id})`)

    return { password: newPassword }
  }

  // 批量删除用户
  async batchDeleteUsers(ids: number[]) {
    const users = await this.userRepository.findBy({ id: In(ids) })
    if (users.length === 0) {
      throw new Error('未找到要删除的用户')
    }

    await this.userRepository.remove(users)

    logger.info(`Batch deleted ${users.length} users`)
  }
}
