import { EntityMetadata } from "../metadata/EntityMetadata";
import { DataSource } from "../data-source/DataSource";
/**
 * Creates EntityMetadata for junction tables of the closure entities.
 * Closure junction tables are tables generated by closure entities.
 */
export declare class ClosureJunctionEntityMetadataBuilder {
    private connection;
    constructor(connection: DataSource);
    /**
     * Builds EntityMetadata for the closure junction of the given closure entity.
     */
    build(parentClosureEntityMetadata: EntityMetadata): EntityMetadata;
}
