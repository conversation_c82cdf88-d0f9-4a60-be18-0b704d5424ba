{"name": "enterprise-desktop-app-server", "version": "1.0.0", "description": "企业级桌面应用后端服务", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts --fix", "test": "jest", "init-data": "ts-node src/scripts/init-data.ts"}, "keywords": ["nodejs", "express", "typescript", "typeorm", "mysql"], "author": "Enterprise Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "typeorm": "^0.3.17", "mysql2": "^3.6.5", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "winston": "^3.11.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}}